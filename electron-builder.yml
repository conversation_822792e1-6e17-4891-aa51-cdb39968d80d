appId: com.afrobet.smartkenoplay
productName: Bana International Bingo
copyright: Copyright © ${author}
directories:
  buildResources: build
files:
  - "!**/.vscode/*"
  - "!src/*"
  - "!electron.vite.config.{js,ts,mjs,cjs}"
  - "!{.eslint<PERSON>che,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}"
  - "!{.env,.env.*,.npmrc,pnpm-lock.yaml}"
  - "!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}"
  - "!test/*"
  - "!e2e/*"
asar: true
asarUnpack:
  - resources/**
win:
  publisherName: Afrobet Inc.
  executableName: Bana International Bingo
  icon: build/icon.ico
  target:
    - target: nsis
      arch:
        - x64
        - ia32
nsis:
  artifactName: ${productName}-Setup-${version}-${arch}.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName} v${version}
  createDesktopShortcut: always
  oneClick: false
  perMachine: true
  allowToChangeInstallationDirectory: true
  runAfterFinish: true
  deleteAppDataOnUninstall: true
mac:
  icon: build/icon.icns
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${productName}-${version}.${ext}
linux:
  icon: build/icon.png
  target:
    - AppImage
    - snap
    - deb
  maintainer: RasBet Inc.
  vendor: RasBet Inc.
  category: Utility
appImage:
  artifactName: ${productName}-${version}.${ext}
npmRebuild: true
