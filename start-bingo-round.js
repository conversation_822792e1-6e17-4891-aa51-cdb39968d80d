import mongoose from "mongoose";
import Event from "./Server/src/models/Event.js";
import { getRandomWinningPattern } from "./Server/src/utils/drawBingoNumbers.js";

console.log("Starting a test Bingo round...");

// Connect to MongoDB
await mongoose.connect("mongodb://127.0.0.1:27017/RasBet");

try {
  // Create a new Bingo round
  const winningPattern = getRandomWinningPattern();
  const gameCode = Math.floor(1000 + Math.random() * 9000).toString();
  const endTime = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now

  const newEvent = new Event({
    round: 1,
    gameCode,
    winningPattern,
    endTime,
    status: "BETTING_OPEN"
  });

  const savedEvent = await newEvent.save();
  
  console.log("✅ Test Bingo round created:");
  console.log(`   Round: ${savedEvent.round}`);
  console.log(`   Game Code: ${savedEvent.gameCode}`);
  console.log(`   Winning Pattern: ${savedEvent.winningPattern}`);
  console.log(`   Status: ${savedEvent.status}`);
  console.log(`   End Time: ${savedEvent.endTime}`);
  
} catch (error) {
  console.error("❌ Error creating test round:", error.message);
} finally {
  await mongoose.disconnect();
  console.log("🔌 Disconnected from database");
}
