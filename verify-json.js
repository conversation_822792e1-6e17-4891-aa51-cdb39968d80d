// Verify the generated JSON file
const fs = require('fs');

try {
  console.log('🔍 Verifying bingo-boards-seed-75.json...\n');
  
  // Read and parse the JSON file
  const jsonData = JSON.parse(fs.readFileSync('bingo-boards-seed-75.json', 'utf8'));
  
  // Check metadata
  console.log('📋 Metadata:');
  console.log(`   Seed: ${jsonData.metadata.seed}`);
  console.log(`   Total Boards: ${jsonData.metadata.totalBoards}`);
  console.log(`   Generated At: ${jsonData.metadata.generatedAt}`);
  console.log(`   Version: ${jsonData.metadata.version}`);
  console.log(`   Description: ${jsonData.metadata.description}`);
  
  // Check boards array
  console.log(`\n📊 Boards Array:`);
  console.log(`   Length: ${jsonData.boards.length}`);
  console.log(`   First Board ID: ${jsonData.boards[0].id}`);
  console.log(`   Last Board ID: ${jsonData.boards[jsonData.boards.length - 1].id}`);
  
  // Verify a few boards
  console.log(`\n🎯 Sample Boards:`);
  
  for (let i = 0; i < 3; i++) {
    const boardData = jsonData.boards[i];
    console.log(`\n   Board #${boardData.id}:`);
    console.log(`   5x5 Grid:`);
    
    boardData.board.forEach((row, rowIndex) => {
      const rowStr = row.map(num => num === 0 ? 'FREE' : num.toString().padStart(2, ' ')).join(' | ');
      console.log(`     ${rowStr}`);
    });
    
    console.log(`   Flat Array: [${boardData.flatArray.slice(0, 10).join(', ')}...]`);
    
    // Verify structure
    const isValid = boardData.board.length === 5 && 
                   boardData.board.every(row => row.length === 5) &&
                   boardData.board[2][2] === 0 &&
                   boardData.flatArray.length === 25;
    
    console.log(`   Valid: ${isValid ? '✅' : '❌'}`);
  }
  
  // File size
  const stats = fs.statSync('bingo-boards-seed-75.json');
  console.log(`\n📁 File Info:`);
  console.log(`   Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   Created: ${stats.birthtime}`);
  
  console.log('\n✅ JSON file verification complete!');
  
} catch (error) {
  console.error('❌ Error verifying JSON file:', error.message);
}
