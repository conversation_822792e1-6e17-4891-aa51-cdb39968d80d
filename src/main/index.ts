import { app, shell, BrowserWindow, ipcMain } from "electron";
import { join } from "path";
import { electronApp, optimizer, is } from "@electron-toolkit/utils";
import icon from "../../resources/icon.ico?asset";
import os from "os";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

let splashWindow: BrowserWindow | null = null;

function createSplashWindow(): void {
  splashWindow = new BrowserWindow({
    width: 800,
    height: 600,
    frame: false,
    fullscreen: true,
    backgroundColor: "#012B5E",
    webPreferences: {
      sandbox: true,
      contextIsolation: true,
    },
  });

  splashWindow
    .loadFile(join(__dirname, "../renderer/splash.html"))
    .catch((err) => {
      console.error("Failed to load splash screen:", err);
    });

  splashWindow.once("ready-to-show", () => {
    if (splashWindow && !splashWindow.isDestroyed()) {
      splashWindow.show();
    }
  });
}

function createMainWindow(): BrowserWindow {
  const mainWindow = new BrowserWindow({
    icon: icon,
    show: false,
    fullscreen: true,
    webPreferences: {
      preload: join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url);
    return { action: "deny" };
  });

  // After a short delay, load the actual app
  setTimeout(() => {
    if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
      mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
    } else {
      mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
    }
  }, 2000);

  return mainWindow;
}

app.whenReady().then(() => {
  electronApp.setAppUserModelId("com.electron");

  // Create splash window immediately
  createSplashWindow();

  // Create main window (hidden)
  const mainWindow = createMainWindow();

  // When main window is ready, replace splash
  mainWindow.once("ready-to-show", () => {
    // Wait for the page to fully load including React content
    mainWindow.webContents.once("did-finish-load", () => {
      // Additional small delay to ensure React has rendered
      setTimeout(() => {
        if (splashWindow && !splashWindow.isDestroyed()) {
          splashWindow.close();
          splashWindow = null;
          mainWindow.show();
        }
      }, 1000); // Small delay for React to render
    });
  });

  // Fallback: if main window takes too long, show it anyway
  setTimeout(() => {
    if (mainWindow && !mainWindow.isVisible()) {
      if (splashWindow && !splashWindow.isDestroyed()) {
        splashWindow.close();
        splashWindow = null;
      }
      mainWindow.show();
    }
  }, 5000); // 5 second fallback

  app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });

  ipcMain.on("ping", () => console.log("pong"));

  // Machine validation IPC handlers
  ipcMain.handle("get-network-interfaces", async () => {
    console.log('🔧 Main: get-network-interfaces called');
    try {
      const networkInterfaces = os.networkInterfaces();
      const interfaces: any[] = [];

      for (const [name, nets] of Object.entries(networkInterfaces)) {
        if (nets) {
          for (const net of nets) {
            // Include ALL interfaces with MAC addresses (even disconnected ones)
            // Only skip loopback/internal interfaces without MAC addresses
            if (net.mac && net.mac !== '00:00:00:00:00:00') {
              interfaces.push({
                name,
                mac: net.mac,
                internal: net.internal,
                family: net.family,
                address: net.address || 'N/A', // Address might be empty for disconnected interfaces
              });
            }
          }
        }
      }

      console.log(`🔧 Found ${interfaces.length} network interfaces with MAC addresses`);
      interfaces.forEach(iface => {
        console.log(`   ${iface.name}: ${iface.mac} (${iface.internal ? 'internal' : 'external'}, ${iface.address})`);
      });

      return interfaces;
    } catch (error) {
      console.error('Error getting network interfaces:', error);
      return [];
    }
  });

  ipcMain.handle("get-mac-addresses", async () => {
    console.log('🔧 Main: get-mac-addresses called');
    try {
      const platform = os.platform();
      let command: string;

      if (platform === 'win32') {
        // Use wmic to get all network adapters including disconnected ones
        command = 'wmic path win32_networkadapter where "MACAddress is not null" get MACAddress /format:csv';
      } else if (platform === 'darwin') {
        command = 'ifconfig | grep ether | awk \'{print $2}\'';
      } else {
        command = 'cat /sys/class/net/*/address';
      }

      const { stdout } = await execAsync(command);
      const macAddresses: string[] = [];

      if (platform === 'win32') {
        // Parse Windows wmic output (CSV format)
        const lines = stdout.trim().split('\n');
        lines.forEach((line) => {
          // Skip header and empty lines
          if (line.includes('MACAddress') || !line.trim()) return;

          const parts = line.split(',');
          // The MAC address is typically in the last column
          const macAddress = parts[parts.length - 1]?.trim();
          if (macAddress && macAddress !== 'N/A' && macAddress.match(/^[0-9A-Fa-f:]{17}$/)) {
            macAddresses.push(macAddress);
          }
        });

        // Fallback to getmac if wmic fails or returns no results
        if (macAddresses.length === 0) {
          console.log('🔧 Fallback: Using getmac command');
          const { stdout: getmacOutput } = await execAsync('getmac /fo csv /nh');
          const getmacLines = getmacOutput.trim().split('\n');
          getmacLines.forEach((line) => {
            const parts = line.replace(/"/g, '').split(',');
            // Include ALL MAC addresses, even from disconnected interfaces
            if (parts.length >= 2 && parts[1] !== 'N/A') {
              macAddresses.push(parts[1]);
            }
          });
        }
      } else {
        // Parse Unix-like output
        const macs = stdout.trim().split('\n').filter(mac => mac && mac !== '00:00:00:00:00:00');
        macAddresses.push(...macs);
      }

      console.log(`🔧 Found ${macAddresses.length} MAC addresses via command`);
      macAddresses.forEach(mac => {
        console.log(`   ${mac}`);
      });

      return macAddresses;
    } catch (error) {
      console.error('Error getting MAC addresses via command:', error);
      return [];
    }
  });

  app.on("activate", function () {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow().show();
    }
  });
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});
