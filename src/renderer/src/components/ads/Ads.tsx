import { useEffect, useState } from 'react'

// Bingo pattern multipliers for display
const PATTERN_MULTIPLIERS = {
  "line": 2,
  "diagonal": 3,
  "four-corners": 4,
  "x-pattern": 5,
  "t-pattern": 6,
  "l-pattern": 7,
  "full-house": 10,
}

// Define pattern display times during countdown (5-minute rounds)
const PATTERN_DISPLAY_TIMES = {
  "line": [280, 220, 160, 100, 40], // Show line pattern at these countdown times
  "diagonal": [270, 210, 150, 90, 30],
  "four-corners": [260, 200, 140, 80, 20],
  "x-pattern": [250, 190, 130, 70, 10],
  "t-pattern": [240, 180, 120, 60],
  "l-pattern": [230, 170, 110, 50],
  "full-house": [275, 215, 155, 95, 35],
}

export const MultipleLevels = () => (
  <div style={{ fontSize: '4.2vw' }} className="mt-5% text-center">
    <div className="text-error Goodtimes">Multiple</div>
    <div className="Goodtimes -my-[2vh]">Winning</div>
    <div className="text-error Goodtimes -py-[2vh]">Patterns</div>
    <div className="-py-[2vh] -my-[2vh] font-semibold" style={{ fontSize: '4vw' }}>
      with different
    </div>
    <div className="Goodtimes -mb-[3vh] tracking-widest text-[#f3f300]" style={{ fontSize: '4vw' }}>
      Prize
    </div>
    <span
      className="Goodtimes -mb-[3vh] tracking-widest text-[#f3f300]"
      style={{ fontSize: '4vw' }}
    >
      Multipliers
    </span>
    <div className="" style={{ fontSize: '4vw' }}>
      in every game!
    </div>
  </div>
)

export const BingoNumbers = () => (
  <div
    style={{ fontSize: '4.5vw' }}
    className="Goodtimes pt-[8vh] text-center leading-[14vh] tracking-wider"
  >
    <span className="text-error">75</span> <span className="pr-[0.5vh]">num</span>
    bers
    <br />
    <span className="pr-[0.5vh]">dra</span>
    <span>wn</span>
    <br />
    until <span className="text-error">BINGO!</span>
  </div>
)

export const BingoPatterns = ({ countdown }: { countdown: number | undefined }) => {
  const [currentPattern, setCurrentPattern] = useState<string>("line")

  useEffect(() => {
    if (countdown === undefined) return

    let foundPattern: string | null = null

    for (const [pattern, times] of Object.entries(PATTERN_DISPLAY_TIMES)) {
      if (times.includes(countdown)) {
        foundPattern = pattern
        break
      }
    }

    if (foundPattern && foundPattern !== currentPattern) {
      setCurrentPattern(foundPattern)
    }
  }, [countdown, currentPattern])

  const displayablePatterns = Object.entries(PATTERN_MULTIPLIERS)
    .map(([pattern, multiplier]) => ({ pattern, multiplier }))
    .sort((a, b) => b.multiplier - a.multiplier) // Display highest multiplier first

  return (
    <div
      className="pattern-table-container"
      style={{
        width: '100%',
        marginTop: '1vh'
      }}
    >
      <div
        style={{
          textAlign: 'center',
          fontSize: '4.3vw'
        }}
        className="Goodtimes text-error"
      >
        {currentPattern.toUpperCase().replace('-', ' ')}
      </div>
      <div
        style={{
          fontSize: '4vw'
        }}
        className="mx-[15%] grid grid-cols-2"
      >
        <div style={{ color: '#f3f300', fontWeight: 'bold' }}>PATTERN</div>
        <div
          style={{
            color: '#f3f300',
            fontWeight: 'bold'
          }}
        >
          MULTIPLIER
        </div>
      </div>
      {displayablePatterns.map((item) => {
        return (
          <div
            key={item.pattern}
            style={{
              color: item.pattern === currentPattern ? '#f3f300' : 'white',
              fontSize: '2.7vw',
              fontWeight: item.pattern === currentPattern ? 'bold' : 'normal'
            }}
            className="mx-[15%] grid grid-cols-2 leading-[5.5vh] md:leading-[7vh]"
          >
            <div>{item.pattern.replace('-', ' ').toUpperCase()}</div>
            <div>{item.multiplier}x</div>
          </div>
        )
      })}
    </div>
  )
}

export const FullHouse = () => (
  <div style={{ fontSize: '4vw' }} className="pt-16 text-center">
    <p className="leading-[5vh]">Play</p>
    <p>
      The <span className="text-error Goodtimes">BINGO</span> Game{' '}
    </p>
    <div className="pt-[4vh] leading-[7vh]">
      <p>
        Get a <span className="text-error">FULL HOUSE</span> <br /> and{' '}
      </p>
      <p>win the</p>
    </div>
    <p className="pt-[6vh]">
      <span className="text-error Goodtimes">BIG</span> PRIZE!
    </p>
  </div>
)

export const BingoGrid = () => (
  <div
    style={{ fontSize: '4.5vw' }}
    className="Goodtimes pt-[8vh] text-center leading-[14vh] tracking-wider"
  >
    <span className="text-error">5x5</span> grid <br />{' '}
    with <span className="text-error">FREE</span> <br /> center space
  </div>
)
