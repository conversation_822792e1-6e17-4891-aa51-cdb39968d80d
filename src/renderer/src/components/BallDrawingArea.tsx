import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";

interface BallDrawingAreaProps {
  currentBall?: number;
  recentBalls: number[];
  isDrawing: boolean;
  className?: string;
}

export const BallDrawingArea: React.FC<BallDrawingAreaProps> = ({
  currentBall,
  recentBalls,
  isDrawing,
  className
}) => {
  // Get the color for a bingo ball based on its number
  const getBallColor = (number: number) => {
    if (number >= 1 && number <= 15) return "bg-blue-500"; // B
    if (number >= 16 && number <= 30) return "bg-purple-500"; // I
    if (number >= 31 && number <= 45) return "bg-red-500"; // N
    if (number >= 46 && number <= 60) return "bg-green-500"; // G
    if (number >= 61 && number <= 75) return "bg-yellow-500"; // O
    return "bg-gray-500";
  };

  const getBallLetter = (number: number) => {
    if (number >= 1 && number <= 15) return "B";
    if (number >= 16 && number <= 30) return "I";
    if (number >= 31 && number <= 45) return "N";
    if (number >= 46 && number <= 60) return "G";
    if (number >= 61 && number <= 75) return "O";
    return "";
  };

  return (
    
  );
};
