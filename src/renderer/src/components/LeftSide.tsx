import { useMemo } from "react";

import { NumberPlate } from "./NumberContainer";
import { NumberPlate2 } from "./NumberContainer2";
import { cn } from "@renderer/lib/utils";

import draw9Img from "../assets/images/9draw.png";
import appLogoImg from "../assets/images/app-logo.png";

export const LeftSideBar = ({
  type,
  outcomes,
  eventNumber,
  isEventOffline,
}: {
  type: "drawing" | "ads";
  outcomes: number[];
  eventNumber?: number;
  isEventOffline: boolean;
}) => {
  const { headCount, tailCount } = useMemo(() => {
    return outcomes.reduce(
      (counts, num) => {
        if (num <= 40) {
          counts.headCount++;
        } else {
          counts.tailCount++;
        }
        return counts;
      },
      { headCount: 0, tailCount: 0 },
    );
  }, [outcomes]);

  return (
    <div className="leftSideBar">
      <div className="flex flex-col pt-[4.2vh]">
        {eventNumber ? (
          <div className="absolute top-[4.2vh] left-[3.5vw] z-50 flex">
            <img src={draw9Img} className="drawTextImage" />
            <p
              style={{
                fontSize: "4.4vw",
                fontFamily: "Gunar",
                lineHeight: 1,
                letterSpacing: "-0.1vw",
              }}
              className="text-shadow mt-[0.5vh] text-white"
            >
              {eventNumber}
            </p>
          </div>
        ) : null}

        <div
          className={cn(
            "Eurostib relative grid h-[7.5vh] w-[24vh] place-items-center self-end bg-cover bg-center bg-no-repeat !text-black",
            eventNumber ? "opacity-100" : "opacity-0",
            outcomes.length === 0
              ? "bg-[url('./assets/images/9red.png')] !text-red-700/20"
              : headCount > tailCount
                ? "bg-[url('./assets/images/9head.png')]"
                : headCount === tailCount
                  ? "bg-[url('./assets/images/9even.png')]"
                  : "bg-[url('./assets/images/9red.png')] !text-red-700/20",
          )}
        >
          <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.7vw] uppercase">
            {outcomes.length === 0
              ? ""
              : headCount === tailCount
                ? "Evens"
                : "Heads"}
          </p>
        </div>
        <div className="Eurostib w-full">
          {type === "ads" ? (
            <NumberPlate2 outcomes={outcomes} isEventOffline={isEventOffline} />
          ) : (
            <NumberPlate outcomes={outcomes} />
          )}
        </div>
        <div
          className={cn(
            "Eurostib relative grid h-[7.5vh] w-[24vh] place-items-center self-end bg-cover bg-center bg-no-repeat !text-black",
            eventNumber ? "opacity-100" : "opacity-0",
            outcomes.length === 0
              ? "bg-[url('./assets/images/9red.png')] !text-red-700/20"
              : tailCount > headCount
                ? "bg-[url('./assets/images/9tail.png')]"
                : headCount === tailCount
                  ? "bg-[url('./assets/images/9even.png')]"
                  : "bg-[url('./assets/images/9red.png')] !text-red-700/20",
          )}
        >
          <p className="absolute top-1/2 left-1/2 -translate-1/2 text-[2.7vw] uppercase">
            {outcomes.length === 0
              ? ""
              : headCount === tailCount
                ? "Evens"
                : "Tails"}
          </p>
        </div>
      </div>
      <img
        src={appLogoImg}
        className="absolute bottom-[6vh] left-[3.5vw] z-50 h-[8.5vh] w-auto object-contain"
      />
    </div>
  );
};
