import { useMemo, useState } from "react";
import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";

interface BingoNumberGridProps {
  drawnNumbers: number[];
  lastDrawnNumber?: number;
  className?: string;
  onNumbersChange?: (numbers: number[]) => void;
}

const plateVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.01,
    },
  },
};

const numberEntryVariants = {
  hidden: {
    scale: 0.5,
    opacity: 0,
  },
  visible: {
    scale: 1,
    opacity: 1,
  },
};

export const BingoNumberGrid: React.FC<BingoNumberGridProps> = ({
  drawnNumbers,
  lastDrawnNumber,
  className,
  onNumbersChange,
}) => {
  const [selectedNumbers, setSelectedNumbers] = useState<number[]>([]);
  const handleClick = (num: number) => {
    const newSelectedNumbers = selectedNumbers.includes(num)
      ? selectedNumbers.filter(existing => existing !== num)
      : [...selectedNumbers, num];

    setSelectedNumbers(newSelectedNumbers);
    onNumbersChange?.(newSelectedNumbers);
  };
  const drawnSet = useMemo(() => new Set(drawnNumbers), [drawnNumbers]);
  const selectedSet = useMemo(() => new Set(selectedNumbers), [selectedNumbers]);
  const rows = 5;
  const cols = 15;

  // BINGO letters for each row
  const bingoLetters = ["B", "I", "N", "G", "O"];
  const bingoColors = [
    { letter: "B", color: "text-blue-400", bg: "bg-blue-900/50" },
    { letter: "I", color: "text-purple-400", bg: "bg-purple-900/50" },
    { letter: "N", color: "text-red-400", bg: "bg-red-900/50" },
    { letter: "G", color: "text-green-400", bg: "bg-green-900/50" },
    { letter: "O", color: "text-yellow-400", bg: "bg-yellow-900/50" },
  ];

  return (
    <div
      className={cn(
        "flex h-full flex-col rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 p-4 shadow-2xl",
        className,
      )}
    >
      {/* Header */}
      {/*<div className="text-center mb-3 flex-shrink-0">*/}
      {/*  <h2 className="text-2xl font-bold text-white mb-1">Number Board</h2>*/}
      {/*  <div className="text-sm text-blue-200">*/}
      {/*    {drawnNumbers.length} of 75 numbers drawn*/}
      {/*  </div>*/}
      {/*</div>*/}

      {/* Grid with BINGO letters on the left */}
      <motion.div
        className="grid min-h-0 flex-1 grid-cols-16 gap-1"
        variants={plateVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Create rows with BINGO letters */}
        {Array.from({ length: rows }, (_, rowIndex) => {
          const rowElements = [];

          // Add BINGO letter for this row
          rowElements.push(
            <div
              key={`letter-${rowIndex}`}
              className={cn(
                "flex h-[8vh] w-[5vw] items-center justify-center rounded-lg py-2 text-[4vh] font-bold",
                bingoColors[rowIndex].color,
                bingoColors[rowIndex].bg,
              )}
            >
              {bingoLetters[rowIndex]}
            </div>,
          );

          // Add numbers for this row
          for (let col = 0; col < cols; col++) {
            const number = rowIndex * cols + col + 1;
            const delay = (rowIndex + col) * 0.01;

            rowElements.push(
              <motion.div
                key={`${rowIndex}-${col}`}
                variants={numberEntryVariants}
                transition={{
                  delay: delay,
                  duration: 0.3,
                }}
                 onClick={() => handleClick(number)}
              >
                <BingoNumber
                  num={number}
                  isDrawn={drawnSet.has(number)}
                  isSelected={selectedSet.has(number)}
                  isLastDrawn={number === lastDrawnNumber}
                />
              </motion.div>,
            );
          }

          return rowElements;
        }).flat()}
      </motion.div>
    </div>
  );
};

interface BingoNumberProps {
  num: number;
  isDrawn: boolean;
  isSelected: boolean;
  isLastDrawn: boolean;
}

const BingoNumber: React.FC<BingoNumberProps> = ({
  num,
  isDrawn,
  isSelected,
  isLastDrawn,
}) => {

  // Determine BINGO column color
  const getColumnColor = (number: number) => {
    if (number >= 1 && number <= 15) return "bg-blue-500"; // B
    if (number >= 16 && number <= 30) return "bg-purple-500"; // I
    if (number >= 31 && number <= 45) return "bg-red-500"; // N
    if (number >= 46 && number <= 60) return "bg-green-500"; // G
    if (number >= 61 && number <= 75) return "bg-yellow-500"; // O
    return "bg-gray-500";
  };

  return (
    <motion.div
      className={cn(
        "relative flex h-[8vh] items-center justify-center rounded text-[4.5vh] font-bold text-white cursor-pointer",
        isDrawn ? getColumnColor(num) : isSelected ? getColumnColor(num) + " opacity-80" : "bg-gray-700 text-gray-300/30 hover:bg-gray-600",
      )}
      animate={
        isLastDrawn
          ? {
              scale: [1, 1.3, 1],
              boxShadow: [
                "0 0 0px rgba(255,255,255,0)",
                "0 0 20px rgba(255,255,255,0.8)",
                "0 0 0px rgba(255,255,255,0)",
              ],
              transition: { duration: 1, repeat: 2 },
            }
          : {}
      }
    >
      <span className="relative z-10">
        {num}
      </span>
      {(isDrawn || isSelected) && (
        <motion.div
          className={cn(
            "absolute inset-0 rounded",
            isDrawn ? "bg-white/20" : "bg-white/10"
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: isDrawn ? [0, 0.5, 0] : 1 }}
          transition={{ duration: isDrawn ? 0.5 : 0.2 }}
        />
      )}
    </motion.div>
  );
};
