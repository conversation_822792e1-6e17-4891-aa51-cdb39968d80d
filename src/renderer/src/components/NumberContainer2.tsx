import { useMemo, useState, useEffect, useRef } from "react";
import { motion } from "motion/react";

import { cn } from "@renderer/lib/utils";

const plateVariants = {
  visible: {
    opacity: 1,
  },
};

export const NumberPlate2 = ({
  outcomes,
  isEventOffline,
}: {
  outcomes: number[];
  isEventOffline: boolean;
}) => {
  const lastNumber = useMemo(() => outcomes[outcomes.length - 1], [outcomes]);
  const outcomeSet = useMemo(() => new Set(outcomes || []), [outcomes]);
  const rows = 8;
  const cols = 10;
  const grid: React.ReactNode[] = [];

  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      const number = row * cols + col + 1;
      grid.push(
        <div
          key={`${row}-${col}`}
          style={{ overflow: "visible", position: "relative" }}
        >
          <Number
            num={number}
            active={outcomeSet.has(number)}
            isLastNumber={number === lastNumber}
            isEventOffline={isEventOffline}
          />
        </div>,
      );
    }
  }

  return (
    <motion.div
      variants={plateVariants}
      initial="visible"
      animate="visible"
      className="relative grid grid-cols-10 gap-[0.175vw] py-[1vh] pl-[0.5vw]"
      style={{
        overflow: "visible",
      }}
    >
      {grid}
    </motion.div>
  );
};

export const Number = ({
  num,
  active,
  isLastNumber,
  isEventOffline,
}: {
  num: number;
  active: boolean;
  isLastNumber?: boolean;
  isEventOffline: boolean;
}) => {
  const [showActiveStyle, setShowActiveStyle] = useState(false);

  // --- START: Added logic for re-triggering animation ---
  const [animationKey, setAnimationKey] = useState(0);
  const prevEventOffline = useRef(isEventOffline);

  useEffect(() => {
    // This effect re-triggers the animation if the event becomes offline while the number is active.
    // We check if `isEventOffline` just changed from false to true.
    if (isEventOffline && !prevEventOffline.current && active) {
      // Incrementing the key tells React to re-mount the component, thus re-playing its animation.
      setAnimationKey((prevKey) => prevKey + 1);
    }
    // Store the current value for the next render.
    prevEventOffline.current = isEventOffline;
  }, [isEventOffline, active]);
  // --- END: Added logic ---

  useEffect(() => {
    if (!active) {
      setShowActiveStyle(false);
    }
  }, [active]);

  const numberClass = useMemo(() => {
    if (!active) {
      return "default-draw";
    }
    if (showActiveStyle) {
      const baseClass = num > 40 ? "tail-draw" : "head-draw";
      return baseClass;
    }
    return "default-draw";
  }, [active, num, showActiveStyle, isLastNumber]);

  const activeAnimationStyles = {
    scale: [1, 2, 1.1, 1.2, 1.05, 1.1, 1],
    opacity: [1, 0.5, 1, 1, 1, 1, 1],
    zIndex: 9999,
  };

  const inactiveAnimationStyles = {
    scale: 1,
    opacity: 1,
    zIndex: 50,
  };

  return (
    <motion.div
      // By changing the key, we force Framer Motion to re-run the animation
      key={animationKey}
      className={cn(
        "flex place-content-center place-items-center items-start justify-center self-center rounded-[2vh] object-center",
        numberClass,
        active && "animated-number",
      )}
      style={{
        width: "100%",
        height: "100%",
        fontSize: "3.3vw",
        overflow: "visible",
        textAlign: "center",
        position: active ? "absolute" : "relative",
        top: active ? "0" : undefined,
        left: active ? "0" : undefined,
        right: active ? "0" : undefined,
        bottom: active ? "0" : undefined,
        transformOrigin: "center center",
      }}
      initial={{
        scale: 1,
        opacity: 1,
        zIndex: 50,
      }}
      animate={active ? activeAnimationStyles : inactiveAnimationStyles}
      transition={
        active
          ? {
              delay: num * 0.007,
              duration: 0.7,
              times: [0, 0.001, 0.25, 0.45, 0.55, 0.65, 0.8],
              ease: "easeInOut",
              onUpdate: (latest: { opacity: number; scale: number }) => {
                if (active && latest.opacity > 0.2 && !showActiveStyle) {
                  setShowActiveStyle(true);
                }
              },
              onComplete: () => {
                if (active) {
                  setShowActiveStyle(true);
                }
              },
            }
          : {
              duration: 0,
            }
      }
    >
      <span className="scale-y-110">{num}</span>
    </motion.div>
  );
};
