import React, { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Shield, ShieldCheck, ShieldX, Computer, Network, Copy, CheckCircle } from 'lucide-react';
import { 
  validateMachineDetailed, 
  getCurrentMacAddresses, 
  getAllowedMacAddresses,
  addAllowedMacAddress 
} from '../lib/machineValidator';

interface MachineValidatorProps {
  onValidationComplete?: (isValid: boolean) => void;
  showDebugInfo?: boolean;
}

const MachineValidator: React.FC<MachineValidatorProps> = ({ 
  onValidationComplete, 
  showDebugInfo = false 
}) => {
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    currentMacs: string[];
    allowedMacs: string[];
    matchedMac?: string;
    error?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [copiedMac, setCopiedMac] = useState<string | null>(null);

  useEffect(() => {
    validateMachine();
  }, []);

  const validateMachine = async () => {
    setIsLoading(true);
    try {
      const result = await validateMachineDetailed();
      setValidationResult(result);
      onValidationComplete?.(result.isValid);
    } catch (error) {
      console.error('Validation error:', error);
      setValidationResult({
        isValid: false,
        currentMacs: [],
        allowedMacs: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      onValidationComplete?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedMac(text);
      setTimeout(() => setCopiedMac(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleAddMacAddress = (macAddress: string) => {
    addAllowedMacAddress(macAddress);
    validateMachine(); // Re-validate after adding
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-900 to-purple-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center text-white"
        >
          <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Validating Machine</h2>
          <p className="text-white/70">Checking MAC address authorization...</p>
        </motion.div>
      </div>
    );
  }

  if (!validationResult) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-900 to-purple-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center text-white"
        >
          <ShieldX className="w-16 h-16 mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Validation Error</h2>
          <p className="text-white/70">Could not validate machine</p>
        </motion.div>
      </div>
    );
  }

  if (!validationResult.isValid) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-900 to-purple-900 p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-2xl w-full bg-white/10 backdrop-blur-lg rounded-xl p-8 text-white"
        >
          <div className="text-center mb-8">
            <ShieldX className="w-20 h-20 mx-auto mb-4 text-red-400" />
            <h1 className="text-3xl font-bold mb-2">Access Denied</h1>
            <p className="text-white/70 text-lg">This machine is not authorized to run this application</p>
          </div>

          {showDebugInfo && (
            <div className="space-y-6">
              {/* Current MAC Addresses */}
              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Computer className="w-5 h-5 mr-2" />
                  Current Machine MAC Addresses
                </h3>
                {validationResult.currentMacs.length > 0 ? (
                  <div className="space-y-2">
                    {validationResult.currentMacs.map((mac, index) => (
                      <div key={index} className="flex items-center justify-between bg-white/5 rounded p-3">
                        <code className="text-yellow-300 font-mono">{mac}</code>
                        <div className="flex gap-2">
                          <button
                            onClick={() => copyToClipboard(mac)}
                            className="p-2 hover:bg-white/10 rounded transition-colors"
                            title="Copy MAC address"
                          >
                            {copiedMac === mac ? (
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            ) : (
                              <Copy className="w-4 h-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleAddMacAddress(mac)}
                            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
                            title="Add to allowed list"
                          >
                            Authorize
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-white/50">No MAC addresses found</p>
                )}
              </div>

              {/* Allowed MAC Addresses */}
              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3 flex items-center">
                  <Network className="w-5 h-5 mr-2" />
                  Authorized MAC Addresses
                </h3>
                <div className="space-y-2">
                  {validationResult.allowedMacs.map((mac, index) => (
                    <div key={index} className="bg-white/5 rounded p-3">
                      <code className="text-green-300 font-mono">{mac}</code>
                    </div>
                  ))}
                </div>
              </div>

              {/* Instructions */}
              <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-2">Developer Instructions</h3>
                <p className="text-sm text-white/80 mb-3">
                  To authorize this machine, add one of the current MAC addresses to the 
                  <code className="bg-white/20 px-1 rounded mx-1">ALLOWED_MAC_ADDRESSES</code> 
                  array in <code className="bg-white/20 px-1 rounded">machineValidator.ts</code>
                </p>
                <p className="text-xs text-white/60">
                  Or click the "Authorize" button next to a MAC address above for temporary authorization.
                </p>
              </div>
            </div>
          )}

          <div className="text-center mt-8">
            <button
              onClick={validateMachine}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold transition-colors"
            >
              Retry Validation
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  // Validation successful
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-900 to-blue-900"
    >
      <div className="text-center text-white">
        <ShieldCheck className="w-20 h-20 mx-auto mb-4 text-green-400" />
        <h1 className="text-3xl font-bold mb-2">Machine Authorized</h1>
        <p className="text-white/70 text-lg mb-4">
          MAC Address: <code className="text-green-300">{validationResult.matchedMac}</code>
        </p>
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
          transition={{ duration: 2 }}
          className="h-1 bg-green-400 rounded-full mx-auto max-w-xs"
        />
      </div>
    </motion.div>
  );
};

export default MachineValidator;
