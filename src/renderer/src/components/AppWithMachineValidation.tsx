import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import MachineValidator from './MachineValidator';
import { useMachineValidation } from '../hooks/useMachineValidation';

// Your existing app components
interface AppWithMachineValidationProps {
  children: React.ReactNode;
  enableValidation?: boolean;
  showDebugInfo?: boolean;
}

const AppWithMachineValidation: React.FC<AppWithMachineValidationProps> = ({ 
  children, 
  enableValidation = true,
  showDebugInfo = false 
}) => {
  const { isValidating, isValid, hasChecked, error } = useMachineValidation(enableValidation);
  const [showApp, setShowApp] = useState(false);

  useEffect(() => {
    if (hasChecked && isValid) {
      // Delay showing the app for a smooth transition
      const timer = setTimeout(() => {
        setShowApp(true);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [hasChecked, isValid]);

  // If validation is disabled, show the app immediately
  if (!enableValidation) {
    return <>{children}</>;
  }

  // Show validation screen while checking or if invalid
  if (isValidating || !hasChecked || !isValid || !showApp) {
    return (
      <MachineValidator 
        onValidationComplete={(valid) => {
          if (valid) {
            setTimeout(() => setShowApp(true), 1500);
          }
        }}
        showDebugInfo={showDebugInfo}
      />
    );
  }

  // Show the main app after successful validation
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="main-app"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.5 }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

export default AppWithMachineValidation;
