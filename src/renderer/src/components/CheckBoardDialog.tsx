import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { cn } from "@renderer/lib/utils";
import { checkBoard, getPatternGrid } from "@renderer/lib/boardUtils";
import {
  getBoardByIndex,
  getTotalBoardCount,
  isValidBoardIndex,
  getRandomBoardIndex,
} from "@renderer/lib/cartelaGenerator";
import type {
  CheckBoardResult,
  WinningPatternType,
} from "@renderer/types/bingo";

interface CheckBoardDialogProps {
  isOpen: boolean;
  onClose: () => void;
  drawnNumbers: number[];
  currentPattern: WinningPatternType;
}

export const CheckBoardDialog: React.FC<CheckBoardDialogProps> = ({
  isOpen,
  onClose,
  drawnNumbers,
  currentPattern,
}) => {
  const [boardId, setBoardId] = useState<string>();

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<CheckBoardResult | null>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setBoardId("");
      setResult(null);
      setIsLoading(false);
    }
  }, [isOpen]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === "Enter") {
        event.preventDefault();
        if (isValidBoardIndex(boardId) && !isLoading) {
          handleCheck();
        }
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, boardId, isLoading]);

  const handleCheck = async () => {
    setIsLoading(true);

    try {
      // Use board ID (which is the same as board index)
      if (!isValidBoardIndex(boardId)) {
        throw new Error(
          `Invalid board ID. Must be between 0 and ${getTotalBoardCount() - 1}`,
        );
      }

      const checkResult = checkBoard(boardId, drawnNumbers, currentPattern);
      setResult(checkResult);
    } catch (error) {
      setResult({
        boardId: boardId,
        board: [],
        isValid: false,
        hasWon: false,
        markedNumbers: [],
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    setIsLoading(false);
  };

  const handleRandomBoard = () => {
    setBoardId(getRandomBoardIndex());
  };

  const patternGrid = getPatternGrid(currentPattern);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Dialog */}
          <motion.div
            className="relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-600/30 max-w-5xl w-full mx-4 max-h-[95vh] overflow-hidden flex flex-col"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 rounded-t-2xl p-4 border-b border-blue-600/30 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl uppercase font-bold text-white mb-1">
                    Bana International Bingo
                  </h2>
                  <p className="text-blue-200 text-sm">
                    Enter board ID to verify winning status
                  </p>
                </div>
                <div className="text-white text-[3.5vh]">
                  Cartela Id: {boardId}
                </div>
                <button
                  onClick={onClose}
                  className="text-white/70 hover:text-white transition-colors p-2 rounded-lg hover:bg-white/10"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content - 50/50 Split Layout */}
            <div className="flex-1 overflow-hidden">
              <div className="grid grid-cols-2 h-full">
                {/* Left Side - Search and Status (50%) */}
                <div className="p-4 space-y-4 overflow-y-auto border-r border-white/10">
                  {/* Input Section */}
                  <div className="space-y-3">
                    <div>
                      <label className="block text-white text-lg font-semibold mb-3">
                        Board ID (0-{getTotalBoardCount() - 1})
                      </label>
                      <div className="space-y-3">
                        <div className="flex gap-2">
                          <input
                            type="text"
                            max={getTotalBoardCount() - 1}
                            value={boardId}
                            onChange={(e) =>
                              setBoardId(
                                e.target.value.toString() === "0"
                                  ? "0"
                                  : parseInt(e.target.value) || "",
                              )
                            }
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                if (isValidBoardIndex(boardId) && !isLoading) {
                                  handleCheck();
                                }
                              }
                            }}
                            placeholder="Enter board ID (Press Enter to check)"
                            className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-mono"
                            disabled={isLoading}
                            autoFocus
                          />
                        </div>
                        <button
                          onClick={handleCheck}
                          disabled={!isValidBoardIndex(boardId) || isLoading}
                          className={cn(
                            "w-full px-4 py-3 rounded-lg font-semibold text-white transition-all",
                            !isValidBoardIndex(boardId) || isLoading
                              ? "bg-gray-600 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-700 hover:scale-105",
                          )}
                        >
                          {isLoading ? (
                            <div className="flex items-center justify-center gap-2">
                              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                              Checking...
                            </div>
                          ) : (
                            "Check Board"
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Current Pattern Info */}
                  </div>
                </div>

                {/* Right Side - Board Display (50%) */}
                <div className="p-4  justify-center">
                  {result && result.isValid && result.board.length > 0 ? (
                    <div className="max-w-md mx-auto">
                      {/* BINGO Header */}
                      <div className="grid grid-cols-5 gap-2 mb-4">
                        {[
                          { letter: "B", color: "bg-blue-600" },
                          { letter: "I", color: "bg-purple-600" },
                          { letter: "N", color: "bg-red-600" },
                          { letter: "G", color: "bg-green-600" },
                          { letter: "O", color: "bg-yellow-600" },
                        ].map(({ letter, color }) => (
                          <div
                            key={letter}
                            className={cn(
                              "text-center text-white font-bold text-xl rounded-lg py-3 shadow-lg",
                              color,
                            )}
                          >
                            {letter}
                          </div>
                        ))}
                      </div>

                      {/* Board Grid */}
                      <div className="grid grid-cols-5 gap-2">
                        {/* Convert column-based board to row-based display */}
                        {Array.from({ length: 5 }, (_, rowIndex) =>
                          Array.from({ length: 5 }, (_, colIndex) => {
                            // Access board data as board[col][row] since it's column-based
                            const number = result.board[colIndex][rowIndex];
                            const isMarked =
                              number !== 80 &&
                              result.markedNumbers.includes(number);
                            const isFree = number === 80;
                            const isPatternRequired =
                              patternGrid[rowIndex][colIndex];
                            // Only highlight if it's part of the pattern AND has been called (or is FREE)
                            const isWinningNumber =
                              isPatternRequired && (isMarked || isFree);
                            const isWinningSquare =
                              result.hasWon && isWinningNumber;

                            return (
                              <motion.div
                                key={`${rowIndex}-${colIndex}`}
                                className={cn(
                                  "aspect-square flex items-center justify-center rounded-lg text-lg font-bold shadow-md border-2 relative",
                                  isFree
                                    ? "bg-gradient-to-br from-green-400 to-green-600 text-black border-green-300"
                                    : isMarked
                                      ? "bg-gradient-to-br from-green-500 to-green-700 text-white border-green-300"
                                      : "bg-white text-black border-gray-300",
                                )}
                                initial={{ scale: 1 }}
                                animate={
                                  isWinningSquare
                                    ? {
                                        scale: [1, 1.1, 1],
                                        boxShadow: [
                                          "0 4px 15px rgba(0,0,0,0.2)",
                                          "0 8px 25px rgba(255, 215, 0, 0.6)",
                                          "0 4px 15px rgba(0,0,0,0.2)",
                                        ],
                                        transition: {
                                          duration: 1,
                                          repeat: Infinity,
                                        },
                                      }
                                    : {}
                                }
                              >
                                {isFree ? (
                                  <div className="text-center relative z-10">
                                    <div className="text-lg">★</div>
                                    <div className="text-xs font-bold">
                                      FREE
                                    </div>
                                  </div>
                                ) : (
                                  <span className="relative z-10">
                                    {number}
                                  </span>
                                )}
                              </motion.div>
                            );
                          }),
                        ).flat()}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-white/50">
                      <div className="text-6xl mb-4">🎯</div>
                      <div className="text-xl font-semibold mb-2">
                        Enter Board ID
                      </div>
                      <div className="text-sm">
                        Board will appear here after checking
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-white/5 rounded-b-2xl p-3 border-t border-white/10 flex-shrink-0">
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setBoardId("");
                    setResult(null);
                  }}
                  className="px-3 py-1 text-white/70 hover:text-white transition-colors text-sm"
                  disabled={isLoading}
                >
                  Clear
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
