import { useMemo } from "react";
import { motion } from "motion/react";

import { cn } from "@renderer/lib/utils";

const plateVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.015,
    },
  },
};

const numberEntryVariants = {
  hidden: {
    scale: 0.5,
    opacity: 0,
    x: "-100vw",
    y: "100vh",
  },
  visible: {
    scale: 1,
    opacity: 1,
    x: 0,
    y: 0,
  },
};

export const NumberPlate = ({ outcomes }: { outcomes: number[] }) => {
  const lastNumber = useMemo(() => outcomes[outcomes.length - 1], [outcomes]);
  const outcomeSet = useMemo(() => new Set(outcomes || []), [outcomes]);
  const rows = 8;
  const cols = 10;
  const grid: React.ReactNode[] = [];

  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      const delay = (row + (cols - 1 - col)) * 0.075;
      // const delay = (row + (cols - 1 - col)) * 0.03;
      grid.push(
        <motion.div
          key={`${row}-${col}`}
          variants={numberEntryVariants}
          transition={{
            delay: delay,
            delayChildren: delay,
            duration: 0.5,
          }}
        >
          <Number
            num={row * cols + col + 1}
            active={outcomeSet.has(row * cols + col + 1)}
            isLastNumber={row * cols + col + 1 === lastNumber}
          />
        </motion.div>,
      );
    }
  }

  return (
    <motion.div
      variants={plateVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-10 gap-[0.175vw] py-[1vh] pl-[0.5vw]"
    >
      {grid}
    </motion.div>
  );
};

export const Number = ({
  num,
  active,
  isLastNumber,
}: {
  num: number;
  active: boolean;
  isLastNumber: boolean;
}) => (
  <motion.div
    className={cn(
      "z-50 grid place-items-center items-center rounded-[2vh]",
      active ? (num > 40 ? "tail-draw" : "head-draw") : "default-draw",
    )}
    style={{
      width: "100%",
      height: "100%",
      fontSize: "3.3vw",
      // Add overflow visible to allow content to extend beyond container boundaries
      overflow: "visible",
      // Add position relative to ensure the number stays properly positioned
      position: "relative",
      // Ensure text is centered
      textAlign: "center",
    }}
    // Only apply animation to the last drawn number when it becomes active
    initial={
      isLastNumber
        ? {
            scale: 2, // Start at 3x scale
            opacity: 1,
            // Ensure the element can expand beyond its container
            zIndex: 1000000000000000,
          }
        : false
    }
    animate={
      isLastNumber
        ? {
            // Frame-by-frame precise animation based on the YouTube video
            // More detailed steps for a perfect replica of the bounce effect
            scale: [
              2, // Frame 1: Initial size
              1.3, // Frame 4: Maximum compression at impact
              1.6, // Frame 7: Maximum height of first bounce
              1.1, // Frame 10: Second compression at impact
              1.2, // Frame 12: Second bounce peak
              1.0, // Frame 14: Third compression at impact
              1.03, // Frame 15: Small third bounce
              1.0, // Frame 18: Rest at normal size
            ],
            opacity: 1, // Maintain full opacity throughout
            // Ensure the element can expand beyond its container
            zIndex: 1000000000000000,
          }
        : {}
    }
    transition={
      isLastNumber
        ? {
            // delay
            duration: 0.8, // Slightly shorter duration to ensure animation completes
            // Precise timing for each frame to match the video exactly
            times: [
              0, // Frame 1
              0.15, // Frame 4
              0.3, // Frame 7
              0.45, // Frame 10
              0.55, // Frame 12
              0.65, // Frame 14
              0.7, // Frame 15
              0.8, // Frame 18
            ],
            // Custom easing function to match the video's physics exactly
            ease: "easeInOut",
            // Ensure animation completes
          }
        : {}
    }
  >
    <span className="scale-y-110">{num}</span>
  </motion.div>
);
