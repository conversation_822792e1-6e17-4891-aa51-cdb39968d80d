import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";
import { useState } from "react";

interface WinningPatternProps {
  pattern: "line" | "diagonal" | "four-corners" | "full-house" | "x-pattern" | "t-pattern" | "l-pattern";
  className?: string;
}



export const WinningPattern: React.FC<WinningPatternProps> = ({ pattern, className }) => {
  
  const patternName = pattern.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase());
  const [patternGrid, setPatternGrid] = useState([
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
  ]);

  const handleClick = (row: number, col: number) => {
    const newGrid = [...patternGrid];
    newGrid[row][col] = newGrid[row][col] === 'yellow' ? '' : 'yellow';
    setPatternGrid(newGrid);
  };

  const handleRightClick = (e: React.MouseEvent, row: number, col: number) => {
    e.preventDefault();
    const newGrid = [...patternGrid];
    newGrid[row][col] = newGrid[row][col] === 'green' ? '' : 'green';
    setPatternGrid(newGrid);
  };

  return (
    
  );
};

// Helper function to get pattern descriptions
function getPatternDescription(pattern: WinningPatternProps['pattern']): string {
  const descriptions = {
    'line': 'Complete any horizontal, vertical, or diagonal line',
    'diagonal': 'Complete both diagonal lines forming an X',
    'four-corners': 'Mark all four corner squares',
    'full-house': 'Mark all numbers on the board',
    'x-pattern': 'Complete both diagonal lines',
    't-pattern': 'Complete the top row and center column',
    'l-pattern': 'Complete the left column and bottom row'
  };

  return descriptions[pattern] || 'Complete the highlighted pattern';
}
