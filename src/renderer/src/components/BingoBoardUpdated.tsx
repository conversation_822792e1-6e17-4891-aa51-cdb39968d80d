import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";
import { useMemo } from "react";
import { getBoardByIndex, getRandomBoardIndex } from "../lib/cartelaGenerator";

interface BingoBoardProps {
  drawnNumbers: number[];
  className?: string;
  boardId?: string;
  boardIndex?: number; // New prop to specify exact board index
}

// BINGO column ranges
const BINGO_RANGES = {
  B: { min: 1, max: 15, col: 0 },
  I: { min: 16, max: 30, col: 1 },
  N: { min: 31, max: 45, col: 2 },
  G: { min: 46, max: 60, col: 3 },
  O: { min: 61, max: 75, col: 4 },
};

// Generate a sample board for display using the cartela generator
const generateSampleBoard = (
  boardId: string = "SAMPLE123",
  boardIndex?: number,
): number[][] => {
  // If boardIndex is provided, use it directly
  if (typeof boardIndex === "number") {
    try {
      return getBoardByIndex(boardIndex);
    } catch (error) {
      console.warn(`Invalid board index ${boardIndex}, using random board`);
      return getBoardByIndex(getRandomBoardIndex());
    }
  }

  // Convert board ID to index for consistent boards
  let seed = 75;
  for (let i = 0; i < boardId.length; i++) {
    seed += boardId.charCodeAt(i) * (i + 1);
  }

  // Use seed to get a consistent board index (0-4999)
  const index = seed % 15000;
  return getBoardByIndex(index);
};

export const BingoBoard: React.FC<BingoBoardProps> = ({
  drawnNumbers,
  className,
  boardId,
  boardIndex,
}) => {
  const drawnSet = new Set(drawnNumbers);

  // Generate board based on boardIndex or boardId
  const sampleBoard = useMemo(() => {
    return generateSampleBoard(boardId, boardIndex);
  }, [boardId, boardIndex]);

  const markedCount = sampleBoard
    .flat()
    .filter((num) => num !== 80 && drawnSet.has(num)).length;

  return (
    <div className={cn("bg-white rounded-xl shadow-lg p-6", className)}>
      {/* Board Info */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Sample Board</h3>
        <div className="text-sm text-gray-600">
          {typeof boardIndex === "number"
            ? `Board #${boardIndex}`
            : `ID: ${boardId || "SAMPLE123"}`}
        </div>
      </div>

      {/* BINGO Header */}
      <div className="grid grid-cols-5 gap-3 mb-6">
        {[
          { letter: "B", color: "bg-blue-600" },
          { letter: "I", color: "bg-purple-600" },
          { letter: "N", color: "bg-red-600" },
          { letter: "G", color: "bg-green-600" },
          { letter: "O", color: "bg-yellow-600" },
        ].map(({ letter, color }) => (
          <div
            key={letter}
            className={cn(
              "text-center text-white font-bold text-3xl rounded-lg py-3 shadow-lg",
              color,
            )}
          >
            {letter}
          </div>
        ))}
      </div>

      {/* Board Grid */}
      <div className="grid grid-cols-5 gap-3 mb-6">
        {/* Convert column-based board to row-based display */}
        {Array.from({ length: 5 }, (_, rowIndex) =>
          Array.from({ length: 5 }, (_, colIndex) => {
            // Access board data as board[col][row] since it's column-based
            const number = sampleBoard[colIndex][rowIndex];
            const isMarked = number !== 80 && drawnSet.has(number);
            const isFree = number === 80;

            return (
              <motion.div
                key={`${rowIndex}-${colIndex}`}
                className={cn(
                  "w-16 h-16 flex items-center justify-center text-lg font-bold rounded-lg border-2 transition-all duration-300",
                  {
                    "bg-gray-800 text-white border-gray-600": isFree,
                    "bg-green-500 text-white border-green-600 shadow-lg":
                      isMarked && !isFree,
                    "bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200":
                      !isMarked && !isFree,
                  },
                )}
                initial={{ scale: 1 }}
                animate={{
                  scale: isMarked ? 1.05 : 1,
                  backgroundColor: isMarked ? "#10b981" : undefined,
                }}
                transition={{ duration: 0.2 }}
              >
                {isFree ? "FREE" : number}
              </motion.div>
            );
          }),
        ).flat()}
      </div>

      {/* Board Stats */}
      <div className="flex justify-between items-center text-sm text-gray-600">
        <span>Numbers Marked: {markedCount}/24</span>
        <span>Progress: {Math.round((markedCount / 24) * 100)}%</span>
      </div>
    </div>
  );
};

export default BingoBoard;
