import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";
import { useState } from "react";

interface GameInfoBarProps {
  round: number;
  gameCode: string;
  maxWin: number;
  className?: string;
  onCheckBoard?: () => void;
}

export const GameInfoBar: React.FC<GameInfoBarProps> = ({
  round,
  gameCode,
  maxWin,
  className,
  onCheckBoard,
}) => {
  const [maxWin2, setMaxWin2] = useState<number>(0);

  const handleMaxwin = (value: any) => {
    setMaxWin2(value.target.value);
  };
  return (
    <motion.div
      className={cn(
        "bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 rounded-xl p-4 text-white shadow-2xl border border-blue-600/30 h-full flex flex-col justify-center",
        className,
      )}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Game Title */}
      <div className="text-center mb-4">
        <div className="text-[4.5vh] uppercase font-bold text-yellow-400 mb-1 tracking-wider">
          Bana International Bingo
        </div>
      </div>

      {/* Round and Code */}

      {/* Prize */}
      <div className="text-center mb-4">
        <div className="text-xs text-blue-200 uppercase tracking-wide mb-1">
          ደራሽ
        </div>
        <div className="flex justify-center">
          <motion.div
            className="text-2xl grid justify-center w-fit font-bold text-yellow-400 mb-1"
            animate={{
              scale: [1, 1.05, 1],
              textShadow: [
                "0 0 10px rgba(255, 255, 0, 0.5)",
                "0 0 20px rgba(255, 255, 0, 0.8)",
                "0 0 10px rgba(255, 255, 0, 0.5)",
              ],
              transition: { duration: 2, repeat: Infinity },
            }}
          >
            <input
              type="text"
              className=" text-[5vh] text-center"
              value={maxWin2}
              onChange={(value) => handleMaxwin(value)}
            />
          </motion.div>
        </div>
      </div>

      {/* Check Board Button */}
      {onCheckBoard && (
        <div className="text-center">
          <motion.button
            onClick={onCheckBoard}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-2 px-4 rounded-lg shadow-lg border border-green-500/30 transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center justify-center gap-2">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-sm">Check Board</span>
            </div>
          </motion.button>
        </div>
      )}
    </motion.div>
  );
};
