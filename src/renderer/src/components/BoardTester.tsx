import React, { useState, useEffect } from 'react';
import {
  getBoardByIndex,
  getBoardAsString,
  searchBoardsByNumbers,
  getTotalBoardCount,
  getBoardGenerationSeed,
  getBoardStats,
  getRandomBoardIndex,
  isValidBoardIndex
} from '../lib/cartelaGenerator';

const BoardTester: React.FC = () => {
  const [boardIndex, setBoardIndex] = useState<number>(0);
  const [currentBoard, setCurrentBoard] = useState<number[][]>([]);
  const [searchNumbers, setSearchNumbers] = useState<string>('');
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [boardStats, setBoardStats] = useState<any>(null);
  const [error, setError] = useState<string>('');

  // Load board when index changes
  useEffect(() => {
    try {
      if (isValidBoardIndex(boardIndex)) {
        const board = getBoardByIndex(boardIndex);
        setCurrentBoard(board);
        setBoardStats(getBoardStats(boardIndex));
        setError('');
      } else {
        setError(`Invalid board index. Must be between 0 and ${getTotalBoardCount() - 1}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, [boardIndex]);

  const handleSearchBoards = () => {
    try {
      const numbers = searchNumbers
        .split(',')
        .map(n => parseInt(n.trim()))
        .filter(n => !isNaN(n) && n >= 1 && n <= 75);

      if (numbers.length === 0) {
        setError('Please enter valid numbers (1-75) separated by commas');
        return;
      }

      const results = searchBoardsByNumbers(numbers);
      setSearchResults(results);
      setError('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search error');
    }
  };

  const handleRandomBoard = () => {
    setBoardIndex(getRandomBoardIndex());
  };

  const renderBoard = (board: number[][]) => {
    const columnHeaders = ['B', 'I', 'N', 'G', 'O'];
    const columnColors = [
      'bg-blue-600',
      'bg-purple-600',
      'bg-red-600',
      'bg-green-600',
      'bg-yellow-600'
    ];

    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* BINGO Header */}
        <div className="grid grid-cols-5 gap-2 mb-4">
          {columnHeaders.map((letter, index) => (
            <div
              key={letter}
              className={`text-center text-white font-bold text-2xl rounded-lg py-2 ${columnColors[index]}`}
            >
              {letter}
            </div>
          ))}
        </div>

        {/* Board Grid */}
        <div className="grid grid-cols-5 gap-2">
          {/* Convert column-based board to row-based display */}
          {Array.from({ length: 5 }, (_, rowIndex) =>
            Array.from({ length: 5 }, (_, colIndex) => {
              // Access board data as board[col][row] since it's column-based
              const number = board[colIndex][rowIndex];
              const isFree = number === 80;
              return (
                <div
                  key={`${rowIndex}-${colIndex}`}
                  className={`
                    w-16 h-16 flex items-center justify-center text-lg font-bold rounded-lg border-2
                    ${isFree
                      ? 'bg-gray-800 text-white border-gray-600'
                      : 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200'
                    }
                  `}
                >
                  {isFree ? 'FREE' : number}
                </div>
              );
            })
          ).flat()}
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-center">Bingo Board Generator Tester</h1>

      {/* Info Panel */}
      <div className="bg-blue-50 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-2">Generator Info</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>Total Boards: {getTotalBoardCount()}</div>
          <div>Generation Seed: {getBoardGenerationSeed()}</div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Board Index Input */}
          <div>
            <label className="block text-sm font-medium mb-2">Board Index (0-4999)</label>
            <div className="flex gap-2">
              <input
                type="number"
                min="0"
                max="4999"
                value={boardIndex}
                onChange={(e) => setBoardIndex(parseInt(e.target.value) || 0)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleRandomBoard}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Random
              </button>
            </div>
          </div>

          {/* Search Numbers */}
          <div>
            <label className="block text-sm font-medium mb-2">Search Numbers (comma-separated)</label>
            <input
              type="text"
              placeholder="e.g., 5, 23, 45"
              value={searchNumbers}
              onChange={(e) => setSearchNumbers(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Search Button */}
          <div className="flex items-end">
            <button
              onClick={handleSearchBoards}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Search Boards
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Current Board Display */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Board #{boardIndex}</h2>
          {currentBoard.length > 0 && renderBoard(currentBoard)}
        </div>

        {/* Board Stats */}
        {boardStats && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Board Statistics</h3>
            <div className="space-y-2 text-sm">
              <div>Index: {boardStats.index}</div>
              <div>Total Numbers: {boardStats.totalNumbers}</div>
              <div>Number Range: {boardStats.numberRanges.min} - {boardStats.numberRanges.max}</div>
              <div className="mt-3">
                <div className="font-medium mb-1">Column Counts:</div>
                <div className="grid grid-cols-5 gap-2 text-xs">
                  {Object.entries(boardStats.columnCounts).map(([col, count]) => (
                    <div key={col} className="text-center">
                      <div className="font-medium">{col}</div>
                      <div>{count}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="bg-green-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">
            Search Results ({searchResults.length} boards found)
          </h3>
          <div className="grid grid-cols-10 gap-2 text-sm">
            {searchResults.slice(0, 50).map((index) => (
              <button
                key={index}
                onClick={() => setBoardIndex(index)}
                className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                {index}
              </button>
            ))}
          </div>
          {searchResults.length > 50 && (
            <p className="text-sm text-gray-600 mt-2">
              Showing first 50 results. Total: {searchResults.length}
            </p>
          )}
        </div>
      )}

      {/* Board as Text */}
      {currentBoard.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 mt-6">
          <h3 className="text-lg font-semibold mb-3">Board as Text</h3>
          <pre className="text-sm font-mono bg-white p-3 rounded border overflow-x-auto">
            {getBoardAsString(boardIndex)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default BoardTester;
