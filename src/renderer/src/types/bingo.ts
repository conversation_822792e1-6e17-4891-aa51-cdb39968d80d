export type BingoGameStatus = {
  round?: number;
  gameCode?: string;
  status: "BETTING_OPEN" | "BETTING_CLOSED" | "DRAWING" | "GAME_OVER";
  endTime: Date;
  maxWin: number;
};

export type BingoGameHistory = {
  _id: string;
  round: number;
  gameCode: string;
  drawnNumbers: number[];
  winningPattern: WinningPatternType;
  winners?: number;
};

export type WinningPatternType =
  | "line"
  | "diagonal"
  | "four-corners"
  | "full-house"
  | "x-pattern"
  | "t-pattern"
  | "l-pattern";

export type BingoColumn = "B" | "I" | "N" | "G" | "O";

export type BingoNumber = {
  number: number;
  column: BingoColumn;
  isDrawn: boolean;
  drawOrder?: number;
};

export type BingoBoard = {
  B: number[];
  I: number[];
  N: number[];
  G: number[];
  O: number[];
};

export type DrawingPhase =
  | "waiting"
  | "intro"
  | "drawing"
  | "revealing"
  | "complete";

export interface BingoGameState {
  gameStatus?: BingoGameStatus;
  drawnNumbers: number[];
  currentBall?: number;
  drawingPhase: DrawingPhase;
  winningPattern: WinningPatternType;
  gameHistory: BingoGameHistory[];
  countdown?: number;
}

// Utility functions
export const getBingoColumn = (number: number): BingoColumn => {
  if (number >= 1 && number <= 15) return "B";
  if (number >= 16 && number <= 30) return "I";
  if (number >= 31 && number <= 45) return "N";
  if (number >= 46 && number <= 60) return "G";
  if (number >= 61 && number <= 75) return "O";
  throw new Error(`Invalid bingo number: ${number}`);
};

export const getBingoColumnRange = (
  column: BingoColumn,
): { min: number; max: number } => {
  switch (column) {
    case "B":
      return { min: 1, max: 15 };
    case "I":
      return { min: 16, max: 30 };
    case "N":
      return { min: 31, max: 45 };
    case "G":
      return { min: 46, max: 60 };
    case "O":
      return { min: 61, max: 75 };
  }
};

// Board checking types
export type CheckBoardResult = {
  boardId: number;
  board: number[][];
  isValid: boolean;
  hasWon: boolean;
  winningPattern?: WinningPatternType;
  winningNumbers?: number[];
  markedNumbers: number[];
  error?: string;
};

export type BoardCheckState = {
  isOpen: boolean;
  isLoading: boolean;
  result?: CheckBoardResult;
};

export const generateBingoBoard = (): BingoBoard => {
  const board: BingoBoard = {
    B: [],
    I: [],
    N: [],
    G: [],
    O: [],
  };

  // Generate random numbers for each column
  Object.keys(board).forEach((column) => {
    const { min, max } = getBingoColumnRange(column as BingoColumn);
    const numbers: number[] = [];

    while (numbers.length < 5) {
      const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
      if (!numbers.includes(randomNum)) {
        numbers.push(randomNum);
      }
    }

    board[column as BingoColumn] = numbers.sort((a, b) => a - b);
  });

  return board;
};
