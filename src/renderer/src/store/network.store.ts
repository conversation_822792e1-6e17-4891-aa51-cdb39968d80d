import axios from "axios";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
  isConnected: boolean;
  actions: {
    initializeApp: () => Promise<void>;
  };
}

export const useNetworkStore = create<State>()(
  devtools((set, _get) => ({
    isConnected: false,
    actions: {
      initializeApp: async () => {
        try {
          const response = await axios.get(
            `${import.meta.env.VITE_BACKEND_API_URL}/v2/health-check`,
          );
          if (response.data) {
            set({ isConnected: true });
          }
          return { isConnected: true };
        } catch (error) {
          set({ isConnected: false });
          throw new Error("Network-Error");
        }
      },
    },
  })),
);

export const useAuthActions = () => useNetworkStore((state) => state.actions);
