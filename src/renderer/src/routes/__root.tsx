import { useEffect } from "react";
import {
  Outlet,
  createRootRouteWithContext,
  useRouter,
} from "@tanstack/react-router";
import type { QueryClient } from "@tanstack/react-query";

import { useNetworkStore } from "@renderer/store/network.store";

const outcomeVideoModules = import.meta.glob("../assets/videos/numbers/*.mp4", {
  query: "?url",
  import: "default",
  eager: true,
});
const numberImageModules = import.meta.glob("../assets/images/numbers/*.png", {
  query: "?url",
  import: "default",
  eager: true,
});

interface MyRouterContext {
  queryClient: QueryClient;
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  beforeLoad: async () => {},
  component: () => {
    // ASSET PRELOADING LOGIC
    useEffect(() => {
      // This effect runs once on component mount to preload all critical assets.
      // This helps prevent lag or content flicker when assets are first displayed.
      const assetsToPreload = [
        // Static Images
        // Dynamically imported assets from Vite's glob import feature
        ...Object.values(outcomeVideoModules),
        ...Object.values(numberImageModules),
      ];

      // Use a Set to avoid adding duplicate preload links
      const preloadedUrls = new Set<string>();

      assetsToPreload.forEach((assetUrl) => {
        // Ensure the URL is a string and hasn't been preloaded already
        if (typeof assetUrl === "string" && !preloadedUrls.has(assetUrl)) {
          const link = document.createElement("link");
          link.rel = "preload";
          link.href = assetUrl;

          // Hint the browser about the type of content for prioritization
          if (assetUrl.endsWith(".png") || assetUrl.endsWith(".jpg")) {
            link.as = "image";
          } else if (assetUrl.endsWith(".mp4")) {
            link.as = "video";
          } else if (assetUrl.endsWith(".mp3")) {
            link.as = "audio";
          }

          // Only append the link if we could determine its type
          if (link.as) {
            document.head.appendChild(link);
            preloadedUrls.add(assetUrl);
          }
        }
      });

      // Optional cleanup: remove the preload links when the component unmounts
      return () => {
        preloadedUrls.forEach((url) => {
          const link = document.querySelector(
            `link[rel="preload"][href="${url}"]`,
          );
          if (link) {
            document.head.removeChild(link);
          }
        });
      };
    }, []);

    return (
      <>
        <Outlet />
      </>
    );
  },
});
