import { createFileRoute } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";

import axios from "axios";
import { Connecting } from "@renderer/components/Connecting";

export const Route = createFileRoute("/network-reconnect")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = Route.useNavigate();


  return <Connecting />;
}
