import React, { useState } from 'react';
import { motion } from 'motion/react';
import { Shield, Computer, Settings, RefreshCw } from 'lucide-react';
import MachineValidator from '../components/MachineValidator';
import { 
  validateMachine, 
  getCurrentMacAddresses, 
  getAllowedMacAddresses,
  validateMachineDetailed 
} from '../lib/machineValidator';

const MachineValidationTest: React.FC = () => {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showValidator, setShowValidator] = useState(false);

  const runValidationTest = async () => {
    setIsLoading(true);
    try {
      const result = await validateMachineDetailed();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        currentMacs: [],
        allowedMacs: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runSimpleValidation = async () => {
    setIsLoading(true);
    try {
      const isValid = await validateMachine();
      const currentMacs = await getCurrentMacAddresses();
      const allowedMacs = getAllowedMacAddresses();
      
      setTestResult({
        isValid,
        currentMacs,
        allowedMacs,
        simple: true,
      });
    } catch (error) {
      setTestResult({
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        currentMacs: [],
        allowedMacs: [],
        simple: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 to-purple-900 p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <Shield className="w-16 h-16 mx-auto mb-4 text-blue-400" />
          <h1 className="text-4xl font-bold text-white mb-2">Machine Validation Test</h1>
          <p className="text-white/70 text-lg">Test the MAC address validation system</p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Test Controls */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6"
          >
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Validation Tests
            </h2>
            
            <div className="space-y-4">
              <button
                onClick={runValidationTest}
                disabled={isLoading}
                className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Computer className="w-4 h-4 mr-2" />
                )}
                Detailed Validation Test
              </button>

              <button
                onClick={runSimpleValidation}
                disabled={isLoading}
                className="w-full px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Shield className="w-4 h-4 mr-2" />
                )}
                Simple Validation Test
              </button>

              <button
                onClick={() => setShowValidator(!showValidator)}
                className="w-full px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
              >
                {showValidator ? 'Hide' : 'Show'} Validation Component
              </button>
            </div>
          </motion.div>

          {/* Test Results */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6"
          >
            <h2 className="text-xl font-semibold text-white mb-4">Test Results</h2>
            
            {testResult ? (
              <div className="space-y-4">
                {/* Validation Status */}
                <div className={`p-4 rounded-lg ${testResult.isValid ? 'bg-green-500/20 border border-green-500/30' : 'bg-red-500/20 border border-red-500/30'}`}>
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${testResult.isValid ? 'bg-green-400' : 'bg-red-400'}`} />
                    <span className="text-white font-medium">
                      {testResult.isValid ? 'Machine Authorized' : 'Access Denied'}
                    </span>
                  </div>
                  {testResult.matchedMac && (
                    <p className="text-sm text-white/70 mt-2">
                      Matched MAC: <code className="text-green-300">{testResult.matchedMac}</code>
                    </p>
                  )}
                </div>

                {/* Current MAC Addresses */}
                {testResult.currentMacs && testResult.currentMacs.length > 0 && (
                  <div>
                    <h3 className="text-white font-medium mb-2">Current MAC Addresses:</h3>
                    <div className="space-y-1">
                      {testResult.currentMacs.map((mac: string, index: number) => (
                        <code key={index} className="block text-sm text-yellow-300 bg-black/20 p-2 rounded">
                          {mac}
                        </code>
                      ))}
                    </div>
                  </div>
                )}

                {/* Allowed MAC Addresses */}
                {testResult.allowedMacs && testResult.allowedMacs.length > 0 && (
                  <div>
                    <h3 className="text-white font-medium mb-2">Allowed MAC Addresses:</h3>
                    <div className="space-y-1">
                      {testResult.allowedMacs.map((mac: string, index: number) => (
                        <code key={index} className="block text-sm text-green-300 bg-black/20 p-2 rounded">
                          {mac}
                        </code>
                      ))}
                    </div>
                  </div>
                )}

                {/* Error */}
                {testResult.error && (
                  <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
                    <p className="text-red-300 text-sm">{testResult.error}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-white/50">Run a test to see results</p>
            )}
          </motion.div>
        </div>

        {/* Validation Component Demo */}
        {showValidator && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6"
          >
            <h2 className="text-xl font-semibold text-white mb-4">Validation Component Demo</h2>
            <div className="bg-black/20 rounded-lg p-4">
              <MachineValidator 
                showDebugInfo={true}
                onValidationComplete={(isValid) => {
                  console.log('Validation completed:', isValid);
                }}
              />
            </div>
          </motion.div>
        )}

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/10 backdrop-blur-lg rounded-xl p-6"
        >
          <h2 className="text-xl font-semibold text-white mb-4">How to Use</h2>
          <div className="text-white/80 space-y-3 text-sm">
            <p>
              <strong>1. Add MAC Address:</strong> Run the test to see your current MAC addresses, 
              then add them to the <code className="bg-black/20 px-1 rounded">ALLOWED_MAC_ADDRESSES</code> array 
              in <code className="bg-black/20 px-1 rounded">machineValidator.ts</code>
            </p>
            <p>
              <strong>2. Integration:</strong> Wrap your main app component with 
              <code className="bg-black/20 px-1 rounded">AppWithMachineValidation</code> to enable validation
            </p>
            <p>
              <strong>3. Testing:</strong> Use the validation component to test and debug MAC address detection
            </p>
            <p>
              <strong>4. Production:</strong> Set <code className="bg-black/20 px-1 rounded">showDebugInfo=false</code> 
              to hide debug information in production
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default MachineValidationTest;
