/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package bingogame;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Collections;
/**
 *
 * <AUTHOR> stars
 */
public class MachineValidator {
     public static boolean validate() {
        try {
            for (NetworkInterface ni : Collections.list(NetworkInterface.getNetworkInterfaces())) {
                byte[] mac = ni.getHardwareAddress();
                if (mac != null) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
                    }
                    //mine laptop computer="58-00-E3-DB-33-27"  SEED=42 geezbingo and top3 bingo
                    //unity bongo desktop"64-00-6A-90-55-B8" SEED=75
                    //hbret(haiscul) bingo desktop "F4-8E-38-AA-2A-B1"SEED=68
                    //abala bingo no cartela no seed"48-D2-24-57-B5-87" ----Z ""
                    String macAddress = sb.toString();
                    if ("58-00-E3-DB-33-27".equals(macAddress)) {
                        return true;
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();    
        }
        return false;
    }
}
