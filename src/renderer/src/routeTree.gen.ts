/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as NetworkReconnectImport } from './routes/network-reconnect'
import { Route as MachineValidationImport } from './routes/machine-validation'
import { Route as BoardTestImport } from './routes/board-test'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const NetworkReconnectRoute = NetworkReconnectImport.update({
  id: '/network-reconnect',
  path: '/network-reconnect',
  getParentRoute: () => rootRoute,
} as any)

const MachineValidationRoute = MachineValidationImport.update({
  id: '/machine-validation',
  path: '/machine-validation',
  getParentRoute: () => rootRoute,
} as any)

const BoardTestRoute = BoardTestImport.update({
  id: '/board-test',
  path: '/board-test',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/board-test': {
      id: '/board-test'
      path: '/board-test'
      fullPath: '/board-test'
      preLoaderRoute: typeof BoardTestImport
      parentRoute: typeof rootRoute
    }
    '/machine-validation': {
      id: '/machine-validation'
      path: '/machine-validation'
      fullPath: '/machine-validation'
      preLoaderRoute: typeof MachineValidationImport
      parentRoute: typeof rootRoute
    }
    '/network-reconnect': {
      id: '/network-reconnect'
      path: '/network-reconnect'
      fullPath: '/network-reconnect'
      preLoaderRoute: typeof NetworkReconnectImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/board-test': typeof BoardTestRoute
  '/machine-validation': typeof MachineValidationRoute
  '/network-reconnect': typeof NetworkReconnectRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/board-test': typeof BoardTestRoute
  '/machine-validation': typeof MachineValidationRoute
  '/network-reconnect': typeof NetworkReconnectRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/board-test': typeof BoardTestRoute
  '/machine-validation': typeof MachineValidationRoute
  '/network-reconnect': typeof NetworkReconnectRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/board-test' | '/machine-validation' | '/network-reconnect'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/board-test' | '/machine-validation' | '/network-reconnect'
  id:
    | '__root__'
    | '/'
    | '/board-test'
    | '/machine-validation'
    | '/network-reconnect'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  BoardTestRoute: typeof BoardTestRoute
  MachineValidationRoute: typeof MachineValidationRoute
  NetworkReconnectRoute: typeof NetworkReconnectRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  BoardTestRoute: BoardTestRoute,
  MachineValidationRoute: MachineValidationRoute,
  NetworkReconnectRoute: NetworkReconnectRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/board-test",
        "/machine-validation",
        "/network-reconnect"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/board-test": {
      "filePath": "board-test.tsx"
    },
    "/machine-validation": {
      "filePath": "machine-validation.tsx"
    },
    "/network-reconnect": {
      "filePath": "network-reconnect.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
