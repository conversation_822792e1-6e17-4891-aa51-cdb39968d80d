import { createRoot } from "react-dom/client";
import {
  QueryClient,
  QueryClientProvider,
  QueryErrorResetBoundary,
} from "@tanstack/react-query";
import {
  createRouter,
  RouterProvider,
  useNavigate,
} from "@tanstack/react-router";
import { ErrorBoundary } from "react-error-boundary";
import { AxiosError } from "axios";

import "./styles.css";
import { routeTree } from "./routeTree.gen";

import { useEffect } from "react";
import { Connecting } from "./components/Connecting";

// Create a new query instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0,
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    },
  },
});

// Registering a global Error for tanstack query
declare module "@tanstack/react-query" {
  interface Register {
    defaultError: AxiosError & {
      response: {
        data: Record<string, any>;
      };
    };
  }
}

// Create a new router instance
export const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 0,
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPendingComponent: Connecting,
  defaultNotFoundComponent: () => {
    const navigate = useNavigate();
    useEffect(() => {
      navigate({ to: "/", replace: true });
    }, []);
    return null;
  },
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

// Render the app
const rootElement = document.getElementById("root");
if (rootElement && !rootElement.innerHTML) {
  const root = createRoot(rootElement);
  root.render(
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ resetErrorBoundary }) => (
            <div className="grid h-full place-items-center gap-2 p-2">
              <p>There was an error!</p>
              <button onClick={() => resetErrorBoundary()}>Try again</button>
            </div>
          )}
        >
            <QueryClientProvider client={queryClient}>
              <RouterProvider router={router} />
            </QueryClientProvider>
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>,
  );
}
