import React, { useState } from "react";
import BingoBoard from "./BingoBoard";

export default function BingoGame() {
  const [calledBalls, setCalledBalls] = useState(["Free"]);
  const [userCartela, setUserCartela] = useState("");
  const [winStatus, setWinStatus] = useState("");

  const handleGameUpdate = (updatedBalls) => {
    setCalledBalls(updatedBalls);
  };

  const checkWinCondition = () => {
    // Implement win checking logic here
    // Access calledBalls array and compare with cartela numbers
  };

  return (
    <div className="bingo-container">
      <BingoBoard onGameUpdate={handleGameUpdate} />
      <div className="control-panel">
        <input
          type="number"
          value={userCartela}
          onChange={(e) => setUserCartela(e.target.value)}
          placeholder="Enter Cartela Number"
        />
        <button onClick={checkWinCondition}>Check Win</button>
        <div className="win-status">{winStatus}</div>
      </div>
    </div>
  );
}
