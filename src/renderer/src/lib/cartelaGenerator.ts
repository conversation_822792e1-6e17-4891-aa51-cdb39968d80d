// Cartela (Bingo Board) Generator - Loads boards from arrays.json
// This file loads pre-generated bingo boards from the arrays.json file

// Import the arrays.json file
import arraysData from '../arrays.json';

// Seeded random number generator for deterministic board generation (kept for compatibility)
export function generateCartelas(seed: number, count: number): number[][] {
  const random = new SeededRandom(seed);
  return Array.from({ length: count }, () =>
    Array.from({ length: 25 }, () => random.nextInt(75) + 1),
  );
}

class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  nextInt(min: number, max: number): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return Math.floor((this.seed / 233280) * (max - min + 1)) + min;
  }
}

// Interface for the arrays.json structure
interface BoardData {
  id: number;
  cartela_no: number;
  bingo_numbers: (number | string)[];
}

// Constants for board generation
const SEED = 75;
const NUM_CARTELAS = arraysData.length; // Use the actual number of boards in arrays.json

// Pre-generated boards cache
let cartelaNumbers: number[][][] | null = null;

// Convert flat array from arrays.json to column-based format
const convertFlatArrayToColumnFormat = (flatArray: (number | string)[]): number[][] => {
  const board: number[][] = [[], [], [], [], []]; // 5 columns

  // The flat array is in row-major order (row by row)
  // We need to convert it to column-major order
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const flatIndex = row * 5 + col;
      let value = flatArray[flatIndex];

      // Convert 'F' to 80 (FREE space)
      if (value === 'F') {
        value = 80;
      }

      board[col][row] = value as number;
    }
  }

  return board;
};

// Initialize the cartela numbers from arrays.json
const initializeCartelaNumbers = (): number[][][] => {
  if (cartelaNumbers) return cartelaNumbers;

  console.log(`Loading ${NUM_CARTELAS} bingo boards from arrays.json...`);
  const boards: number[][][] = [];

  // Load boards from arrays.json
  for (let cartelaIndex = 0; cartelaIndex < NUM_CARTELAS; cartelaIndex++) {
    const boardData = arraysData[cartelaIndex] as BoardData;

    // Convert the flat array to column-based format
    const board = convertFlatArrayToColumnFormat(boardData.bingo_numbers);

    boards.push(board);
  }

  cartelaNumbers = boards;
  console.log(`Loaded ${boards.length} bingo boards from arrays.json successfully.`);
  console.log('Sample board (first board):', boards[0]);
  return boards;
};

// Get a specific board by index (0 to NUM_CARTELAS-1)
export const getBoardByIndex = (index: number): number[][] => {
  if (index < 0 || index >= NUM_CARTELAS) {
    throw new Error(`Board index must be between 0 and ${NUM_CARTELAS - 1}`);
  }

  const boards = initializeCartelaNumbers();
  return boards[index];
};

// Convert 5x5 board to flat array (for compatibility with Java format)
// Since board is now in column format, we need to transpose to get row-wise flat array
export const boardToFlatArray = (board: number[][]): number[] => {
  const flatArray: number[] = [];
  // Convert column-based board to row-wise flat array
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      flatArray.push(board[col][row]);
    }
  }
  return flatArray;
};

// Convert flat array to 5x5 board (column-based format)
export const flatArrayToBoard = (flatArray: number[]): number[][] => {
  if (flatArray.length !== 25) {
    throw new Error("Flat array must have exactly 25 elements");
  }

  const board: number[][] = [];
  // Create column-based board from row-wise flat array
  for (let col = 0; col < 5; col++) {
    const column: number[] = [];
    for (let row = 0; row < 5; row++) {
      column.push(flatArray[row * 5 + col]);
    }
    board.push(column);
  }
  return board;
};

// Search for boards that contain specific numbers
export const searchBoardsByNumbers = (searchNumbers: number[]): number[] => {
  const boards = initializeCartelaNumbers();
  const matchingIndices: number[] = [];

  for (let i = 0; i < boards.length; i++) {
    const flatBoard = boardToFlatArray(boards[i]);
    const hasAllNumbers = searchNumbers.every((num) => flatBoard.includes(num));

    if (hasAllNumbers) {
      matchingIndices.push(i);
    }
  }

  return matchingIndices;
};

// Search for boards that contain any of the specified numbers
export const searchBoardsByAnyNumbers = (searchNumbers: number[]): number[] => {
  const boards = initializeCartelaNumbers();
  const matchingIndices: number[] = [];

  for (let i = 0; i < boards.length; i++) {
    const flatBoard = boardToFlatArray(boards[i]);
    const hasAnyNumber = searchNumbers.some((num) => flatBoard.includes(num));

    if (hasAnyNumber) {
      matchingIndices.push(i);
    }
  }

  return matchingIndices;
};

// Get board numbers as a formatted string (for display)
export const getBoardAsString = (index: number): string => {
  const board = getBoardByIndex(index);
  // Convert column-based board to row-based display
  const rows: string[] = [];
  for (let row = 0; row < 5; row++) {
    const rowValues: string[] = [];
    for (let col = 0; col < 5; col++) {
      const num = board[col][row];
      rowValues.push(num === 80 ? "FREE" : num.toString().padStart(2, " "));
    }
    rows.push(rowValues.join(" | "));
  }
  return rows.join("\n");
};

// Get all cartela numbers (for debugging/testing)
export const getAllCartelaNumbers = (): number[][][] => {
  return initializeCartelaNumbers();
};

// Get total number of available boards
export const getTotalBoardCount = (): number => {
  return NUM_CARTELAS;
};

// Get the seed used for board generation
export const getBoardGenerationSeed = (): number => {
  return SEED;
};

// Validate if a board index is valid
export const isValidBoardIndex = (index: number): boolean => {
  return index >= 0 && index < NUM_CARTELAS;
};

// Get random board index
export const getRandomBoardIndex = (): number => {
  return Math.floor(Math.random() * NUM_CARTELAS);
};

// Get board statistics
export const getBoardStats = (
  index: number,
): {
  index: number;
  totalNumbers: number;
  columnCounts: { B: number; I: number; N: number; G: number; O: number };
  numberRanges: { min: number; max: number };
} => {
  const board = getBoardByIndex(index);
  const flatBoard = boardToFlatArray(board);
  const numbers = flatBoard.filter((n) => n !== 80);

  const columnCounts = {
    B: board[0].filter((n) => n !== 80).length, // B column is board[0]
    I: board[1].filter((n) => n !== 80).length, // I column is board[1]
    N: board[2].filter((n) => n !== 80).length, // N column is board[2]
    G: board[3].filter((n) => n !== 80).length, // G column is board[3]
    O: board[4].filter((n) => n !== 80).length, // O column is board[4]
  };

  return {
    index,
    totalNumbers: numbers.length,
    columnCounts,
    numberRanges: {
      min: Math.min(...numbers),
      max: Math.max(...numbers),
    },
  };
};

// Initialize boards on module load for better performance
// This will generate all boards when the module is first imported
setTimeout(() => {
  initializeCartelaNumbers();
}, 0);
