// Cartela (Bingo Board) Generator - Loads boards from arrays.json
// This file loads pre-generated bingo boards from the arrays.json file

// Seeded random number generator for deterministic board generation
export function generateCartelas(seed: number, count: number): number[][] {
  const random = new SeededRandom(seed);
  return Array.from({ length: count }, () =>
    Array.from({ length: 25 }, () => random.nextInt(75) + 1),
  );
}

class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  nextInt(min: number, max: number): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return Math.floor((this.seed / 233280) * (max - min + 1)) + min;
  }
}

// Interface for the arrays.json structure
interface BoardData {
  id: number;
  cartela_no: number;
  bingo_numbers: (number | string)[];
}

// Arrays data cache
let arraysData: BoardData[] | null = null;

// Function to load and parse arrays data from arrays.json
const loadArraysData = (): BoardData[] => {
  if (arraysData) return arraysData;

  try {
    // Load the arrays.json file using Node.js fs module
    const fs = window.require('fs');
    const path = window.require('path');

    // Get the path to arrays.json - try multiple possible locations
    const possiblePaths = [
      path.join(__dirname, '../../src/renderer/src/arrays.json'),
      path.join(process.cwd(), 'src/renderer/src/arrays.json'),
      path.join(__dirname, '../arrays.json'),
      './src/renderer/src/arrays.json'
    ];

    let arraysPath = '';
    let fileContent = '';

    // Try each path until we find the file
    for (const testPath of possiblePaths) {
      try {
        if (fs.existsSync(testPath)) {
          arraysPath = testPath;
          fileContent = fs.readFileSync(testPath, 'utf8');
          break;
        }
      } catch (e) {
        continue;
      }
    }

    if (!fileContent) {
      throw new Error('arrays.json file not found');
    }

    console.log('📁 Loading arrays.json from:', arraysPath);

    // Parse the JSON
    const parsedData = JSON.parse(fileContent);

    console.log(`✅ Successfully loaded ${parsedData.length} boards from arrays.json`);
    console.log('📋 Sample board:', parsedData[0]);

    arraysData = parsedData as BoardData[];
    return arraysData;
  } catch (error) {
    console.error('❌ Failed to load arrays.json:', error);
    console.warn('⚠️  Falling back to random generation');

    // Return empty array to trigger fallback
    arraysData = [];
    return arraysData;
  }
};

// Convert flat array from arrays.json to column-based format
const convertFlatArrayToColumnFormat = (flatArray: (number | string)[]): number[][] => {
  const board: number[][] = [[], [], [], [], []]; // 5 columns

  // The flat array is in row-major order (row by row)
  // We need to convert it to column-major order
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const flatIndex = row * 5 + col;
      let value = flatArray[flatIndex];

      // Convert 'F' to 80 (FREE space)
      if (value === 'F') {
        value = 80;
      }

      board[col][row] = value as number;
    }
  }

  return board;
};

// Constants for board generation
const SEED = 75;
let NUM_CARTELAS = 15000; // Will be updated when arrays.json is loaded

// Pre-generated boards cache
let cartelaNumbers: number[][][] | null = null;

// Initialize the cartela numbers from arrays.json
const initializeCartelaNumbers = (): number[][][] => {
  if (cartelaNumbers) return cartelaNumbers;

  // Try to load from arrays.json first
  const loadedArraysData = loadArraysData();

  if (loadedArraysData.length > 0) {
    // Use arrays.json data
    NUM_CARTELAS = loadedArraysData.length;
    console.log(`📁 Loading ${NUM_CARTELAS} bingo boards from arrays.json...`);
    const boards: number[][][] = [];

    // Load boards from arrays.json
    for (let cartelaIndex = 0; cartelaIndex < NUM_CARTELAS; cartelaIndex++) {
      const boardData = loadedArraysData[cartelaIndex];

      // Convert the flat array to column-based format
      const board = convertFlatArrayToColumnFormat(boardData.bingo_numbers);

      boards.push(board);
    }

    cartelaNumbers = boards;
    console.log(`✅ Loaded ${boards.length} bingo boards from arrays.json successfully.`);
    console.log('📋 Sample board (first board):', boards[0]);
    return boards;
  } else {
    // Fallback to random generation
    return generateRandomBoards();
  }
};

// Generate random boards (fallback function)
const generateRandomBoards = (): number[][][] => {
  console.log(`🎲 Generating ${NUM_CARTELAS} bingo boards with seed ${SEED}...`);
  const random = new SeededRandom(SEED);
  const boards: number[][][] = [];

  // Generate boards using random generation
  for (let cartelaIndex = 0; cartelaIndex < NUM_CARTELAS; cartelaIndex++) {
    const board: number[][] = [];

    // Pre-generate unique numbers for each column
    const columnNumbers: number[][] = [];

    // Generate unique numbers for each column (B-I-N-G-O)
    for (let col = 0; col < 5; col++) {
      let min: number, max: number;

      switch (col) {
        case 0: // B column
          min = 1;
          max = 15;
          break;
        case 1: // I column
          min = 16;
          max = 30;
          break;
        case 2: // N column
          min = 31;
          max = 45;
          break;
        case 3: // G column
          min = 46;
          max = 60;
          break;
        case 4: // O column
          min = 61;
          max = 75;
          break;
        default:
          min = 1;
          max = 75;
      }

      // Generate 5 unique numbers for this column (excluding center for N column)
      const colNums: number[] = [];
      const numNeeded = col === 2 ? 4 : 5; // N column only needs 4 numbers (center is FREE)

      while (colNums.length < numNeeded) {
        const number = random.nextInt(min, max);
        if (!colNums.includes(number)) {
          colNums.push(number);
        }
      }

      columnNumbers.push(colNums);
    }

    // Build the 5x5 board using the pre-generated column numbers
    // Each array in the board represents a column (B-I-N-G-O)
    for (let col = 0; col < 5; col++) {
      const boardColumn: number[] = [];

      for (let row = 0; row < 5; row++) {
        if (row === 2 && col === 2) {
          // Center space is FREE (represented as 80)
          boardColumn.push(80);
        } else {
          // Use pre-generated numbers for this column
          const colIndex = col === 2 && row > 2 ? row - 1 : row; // Adjust for FREE space in N column
          const number = columnNumbers[col][colIndex];
          boardColumn.push(number);
        }
      }

      board.push(boardColumn);
    }

    boards.push(board);
  }

  cartelaNumbers = boards;
  console.log('📋 Sample board (first board):', boards[0]);
  console.log(`✅ Generated ${boards.length} bingo boards successfully.`);
  return boards;
};

// Get a specific board by index (0-4999)
export const getBoardByIndex = (index: number): number[][] => {
  if (index < 0 || index >= NUM_CARTELAS) {
    throw new Error(`Board index must be between 0 and ${NUM_CARTELAS - 1}`);
  }

  const boards = initializeCartelaNumbers();
  return boards[index];
};

// Convert 5x5 board to flat array (for compatibility with Java format)
// Since board is now in column format, we need to transpose to get row-wise flat array
export const boardToFlatArray = (board: number[][]): number[] => {
  const flatArray: number[] = [];
  // Convert column-based board to row-wise flat array
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      flatArray.push(board[col][row]);
    }
  }
  return flatArray;
};

// Convert flat array to 5x5 board (column-based format)
export const flatArrayToBoard = (flatArray: number[]): number[][] => {
  if (flatArray.length !== 25) {
    throw new Error("Flat array must have exactly 25 elements");
  }

  const board: number[][] = [];
  // Create column-based board from row-wise flat array
  for (let col = 0; col < 5; col++) {
    const column: number[] = [];
    for (let row = 0; row < 5; row++) {
      column.push(flatArray[row * 5 + col]);
    }
    board.push(column);
  }
  return board;
};

// Search for boards that contain specific numbers
export const searchBoardsByNumbers = (searchNumbers: number[]): number[] => {
  const boards = initializeCartelaNumbers();
  const matchingIndices: number[] = [];

  for (let i = 0; i < boards.length; i++) {
    const flatBoard = boardToFlatArray(boards[i]);
    const hasAllNumbers = searchNumbers.every((num) => flatBoard.includes(num));

    if (hasAllNumbers) {
      matchingIndices.push(i);
    }
  }

  return matchingIndices;
};

// Search for boards that contain any of the specified numbers
export const searchBoardsByAnyNumbers = (searchNumbers: number[]): number[] => {
  const boards = initializeCartelaNumbers();
  const matchingIndices: number[] = [];

  for (let i = 0; i < boards.length; i++) {
    const flatBoard = boardToFlatArray(boards[i]);
    const hasAnyNumber = searchNumbers.some((num) => flatBoard.includes(num));

    if (hasAnyNumber) {
      matchingIndices.push(i);
    }
  }

  return matchingIndices;
};

// Get board numbers as a formatted string (for display)
export const getBoardAsString = (index: number): string => {
  const board = getBoardByIndex(index);
  // Convert column-based board to row-based display
  const rows: string[] = [];
  for (let row = 0; row < 5; row++) {
    const rowValues: string[] = [];
    for (let col = 0; col < 5; col++) {
      const num = board[col][row];
      rowValues.push(num === 80 ? "FREE" : num.toString().padStart(2, " "));
    }
    rows.push(rowValues.join(" | "));
  }
  return rows.join("\n");
};

// Get all cartela numbers (for debugging/testing)
export const getAllCartelaNumbers = (): number[][][] => {
  return initializeCartelaNumbers();
};

// Get total number of available boards
export const getTotalBoardCount = (): number => {
  return NUM_CARTELAS;
};

// Get the seed used for board generation
export const getBoardGenerationSeed = (): number => {
  return SEED;
};

// Validate if a board index is valid
export const isValidBoardIndex = (index: number): boolean => {
  return index >= 0 && index < NUM_CARTELAS;
};

// Get random board index
export const getRandomBoardIndex = (): number => {
  return Math.floor(Math.random() * NUM_CARTELAS);
};

// Get board statistics
export const getBoardStats = (
  index: number,
): {
  index: number;
  totalNumbers: number;
  columnCounts: { B: number; I: number; N: number; G: number; O: number };
  numberRanges: { min: number; max: number };
} => {
  const board = getBoardByIndex(index);
  const flatBoard = boardToFlatArray(board);
  const numbers = flatBoard.filter((n) => n !== 80);

  const columnCounts = {
    B: board[0].filter((n) => n !== 80).length, // B column is board[0]
    I: board[1].filter((n) => n !== 80).length, // I column is board[1]
    N: board[2].filter((n) => n !== 80).length, // N column is board[2]
    G: board[3].filter((n) => n !== 80).length, // G column is board[3]
    O: board[4].filter((n) => n !== 80).length, // O column is board[4]
  };

  return {
    index,
    totalNumbers: numbers.length,
    columnCounts,
    numberRanges: {
      min: Math.min(...numbers),
      max: Math.max(...numbers),
    },
  };
};

// Initialize boards on module load for better performance
// This will generate all boards when the module is first imported
setTimeout(() => {
  initializeCartelaNumbers();
}, 0);
