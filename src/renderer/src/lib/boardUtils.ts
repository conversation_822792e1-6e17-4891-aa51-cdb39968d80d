import type { WinningPatternType, CheckBoardResult } from "../types/bingo";
import { getBoardByIndex, isValidBoardIndex } from "./cartelaGenerator";
// Seeded random number generator for deterministic board generation
class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }
}

// Generate a deterministic bingo board from a board ID using cartela generator
export const generateBoardFromId = (boardId: string): number[][] => {
  // Convert board ID to a numeric seed
  let seed = 75;
  for (let i = 0; i < boardId.length; i++) {
    seed += boardId.charCodeAt(i) * (i + 1);
  }

  // Use seed to get a consistent board index (0-14999)
  const index = seed % 15000;
  try {
    return getBoardByIndex(index);
  } catch (error) {
    console.warn(`Invalid board index ${index}, using random board`);
    return getBoardByIndex(getRandomBoardIndex());
  }
};

// Define winning patterns (true = required for win)
const WINNING_PATTERNS = {
  line: [
    [true, true, true, true, true],
    [false, false, false, false, false],
    [false, false, false, false, false],
    [false, false, false, false, false],
    [false, false, false, false, false],
  ],
  diagonal: [
    [true, false, false, false, true],
    [false, true, false, true, false],
    [false, false, true, false, false],
    [false, true, false, true, false],
    [true, false, false, false, true],
  ],
  "four-corners": [
    [true, false, false, false, true],
    [false, false, false, false, false],
    [false, false, false, false, false],
    [false, false, false, false, false],
    [true, false, false, false, true],
  ],
  "full-house": [
    [true, true, true, true, true],
    [true, true, true, true, true],
    [true, true, true, true, true],
    [true, true, true, true, true],
    [true, true, true, true, true],
  ],
  "x-pattern": [
    [true, false, false, false, true],
    [false, true, false, true, false],
    [false, false, true, false, false],
    [false, true, false, true, false],
    [true, false, false, false, true],
  ],
  "t-pattern": [
    [true, true, true, true, true],
    [false, false, true, false, false],
    [false, false, true, false, false],
    [false, false, true, false, false],
    [false, false, true, false, false],
  ],
  "l-pattern": [
    [true, false, false, false, false],
    [true, false, false, false, false],
    [true, false, false, false, false],
    [true, false, false, false, false],
    [true, true, true, true, true],
  ],
};

// Check if a board has won with the given pattern and drawn numbers
export const checkBoardWin = (
  board: number[][],
  drawnNumbers: number[],
  pattern: WinningPatternType,
): boolean => {
  const drawnSet = new Set(drawnNumbers);
  const patternGrid = WINNING_PATTERNS[pattern];

  if (!patternGrid) {
    return false;
  }

  // Check if all required positions in the pattern are marked
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      if (patternGrid[row][col]) {
        // Access board data as board[col][row] since it's column-based
        const number = board[col][row];
        // FREE space (80) is always considered marked
        if (number !== 80 && !drawnSet.has(number)) {
          return false;
        }
      }
    }
  }

  return true;
};

// Get all marked numbers on a board
export const getMarkedNumbers = (
  board: number[][],
  drawnNumbers: number[],
): number[] => {
  const drawnSet = new Set(drawnNumbers);
  const marked: number[] = [];

  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      // Access board data as board[col][row] since it's column-based
      const number = board[col][row];
      if (number !== 80 && drawnSet.has(number)) {
        marked.push(number);
      }
    }
  }

  return marked.sort((a, b) => a - b);
};

// Get winning numbers for a specific pattern
export const getWinningNumbers = (
  board: number[][],
  drawnNumbers: number[],
  pattern: WinningPatternType,
): number[] => {
  const drawnSet = new Set(drawnNumbers);
  const patternGrid = WINNING_PATTERNS[pattern];
  const winningNumbers: number[] = [];

  if (!patternGrid) {
    return winningNumbers;
  }

  // Get numbers that are both in the pattern and drawn
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      if (patternGrid[row][col]) {
        // Access board data as board[col][row] since it's column-based
        const number = board[col][row];
        if (number === 80) {
          // FREE space - always counts as winning
          winningNumbers.push(80);
        } else if (drawnSet.has(number)) {
          // Drawn number in pattern position
          winningNumbers.push(number);
        }
      }
    }
  }

  return winningNumbers.sort((a, b) => a - b);
};

// Enhanced board win check that returns detailed result
export const checkBoardWinDetailed = (
  board: number[][],
  drawnNumbers: number[],
  pattern: WinningPatternType,
): { isWinner: boolean; winningNumbers?: number[] } => {
  const isWinner = checkBoardWin(board, drawnNumbers, pattern);
  const winningNumbers = isWinner
    ? getWinningNumbers(board, drawnNumbers, pattern)
    : undefined;

  return {
    isWinner,
    winningNumbers,
  };
};

// Check if a cartela number is valid
export const isValidCartelaNumber = (cartelaNo: number): boolean => {
  const { isValidCartelaNumber: checkValid } = require('./cartelaGenerator');
  return checkValid(cartelaNo);
};

// Get the valid cartela number range
export const getCartelaNumberRange = (): { min: number, max: number } => {
  const { getCartelaNumberRange: getRange } = require('./cartelaGenerator');
  return getRange();
};

// Check if a board ID is valid (basic validation)

// Main function to check a board using cartela number
export const checkBoard = (
  cartelaNo: number,
  drawnNumbers: number[],
  currentPattern: WinningPatternType,
): CheckBoardResult => {
  try {
    // Import the new cartela functions
    const { getBoardByCartelaNumber, isValidCartelaNumber } = require('./cartelaGenerator');

    if (!isValidCartelaNumber(cartelaNo)) {
      throw new Error(`Invalid cartela number: ${cartelaNo}`);
    }

    const result = getBoardByCartelaNumber(cartelaNo);
    if (!result) {
      throw new Error(`Cartela number ${cartelaNo} not found`);
    }

    const { board, index } = result;

    // Get marked numbers
    const markedNumbers = getMarkedNumbers(board, drawnNumbers);

    // Check if board has won
    const winResult = checkBoardWinDetailed(
      board,
      drawnNumbers,
      currentPattern,
    );
    const hasWon = winResult.isWinner;

    return {
      boardId: cartelaNo, // Now represents cartela number, not array index
      board,
      isValid: true,
      hasWon,
      winningPattern: hasWon ? currentPattern : undefined,
      winningNumbers: winResult.winningNumbers,
      markedNumbers,
    };
  } catch (error) {
    return {
      boardId: cartelaNo,
      board: [],
      isValid: false,
      hasWon: false,
      markedNumbers: [],
      error: `Error checking board: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
};

// Legacy function for backward compatibility (using array index)
export const checkBoardByIndex = (
  boardIndex: number,
  drawnNumbers: number[],
  currentPattern: WinningPatternType,
): CheckBoardResult => {
  try {
    if (!isValidBoardIndex(boardIndex)) {
      throw new Error(`Invalid board index: ${boardIndex}`);
    }

    const board = getBoardByIndex(boardIndex);

    // Get marked numbers
    const markedNumbers = getMarkedNumbers(board, drawnNumbers);

    // Check if board has won
    const winResult = checkBoardWinDetailed(
      board,
      drawnNumbers,
      currentPattern,
    );
    const hasWon = winResult.isWinner;

    return {
      boardId: boardIndex,
      board,
      isValid: true,
      hasWon,
      winningPattern: hasWon ? currentPattern : undefined,
      winningNumbers: winResult.winningNumbers,
      markedNumbers,
    };
  } catch (error) {
    return {
      boardId: boardIndex,
      board: [],
      isValid: false,
      hasWon: false,
      markedNumbers: [],
      error: `Error checking board: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
};

// Get pattern grid for highlighting
export const getPatternGrid = (pattern: WinningPatternType): boolean[][] => {
  return WINNING_PATTERNS[pattern] || WINNING_PATTERNS.line;
};
