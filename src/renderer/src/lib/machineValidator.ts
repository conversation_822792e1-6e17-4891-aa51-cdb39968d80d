// Machine Validator - Validates MAC addresses to restrict app usage to specific machines
// Converted from Java MachineValidator.java

// Hardcoded allowed MAC addresses (you can modify these)
const ALLOWED_MAC_ADDRESSES = [
  "1C-CE-51-BD-FD-4A", // current development machine
  "00-23-14-6E-36-18",
  "BC-85-56-C4-E7-73", // Other
  "BC-85-56-C4-E7-74", // Other
];

// Development mode: still check MAC but allow fallback (set to false for production)
const DEVELOPMENT_MODE = false;

// Development fallback MAC addresses (for when running in browser)
const DEVELOPMENT_FALLBACK_MACS = [
  // "1C-CE-51-BD-FD-4A", // current development machine
];

// Interface for network interface information
interface NetworkInterface {
  name: string;
  mac: string;
  internal: boolean;
  family: string;
  address: string;
}

// Get all network interfaces and their MAC addresses
const getNetworkInterfaces = async (): Promise<NetworkInterface[]> => {
  try {
    console.log(
      "🔍 Checking window.api:",
      typeof window !== "undefined" ? !!window.api : "window undefined",
    );

    // Check if we're in Electron environment
    if (typeof window !== "undefined" && window.api) {
      console.log("🔧 Using Electron IPC to get network interfaces");
      // Use Electron IPC to get network interfaces
      const interfaces = await window.api.getNetworkInterfaces();
      console.log("📊 Received interfaces from IPC:", interfaces);
      return interfaces.map((iface: any) => ({
        name: iface.name,
        mac: iface.mac.toUpperCase().replace(/:/g, "-"),
        internal: iface.internal,
        family: iface.family,
        address: iface.address || "Disconnected", // Handle disconnected interfaces
      }));
    }

    // Fallback: try direct Node.js access (if available)
    if (typeof require !== "undefined") {
      const os = require("os");
      const networkInterfaces = os.networkInterfaces();
      const interfaces: NetworkInterface[] = [];

      for (const [name, nets] of Object.entries(networkInterfaces)) {
        if (nets) {
          for (const net of nets as any[]) {
            // Skip internal interfaces and IPv6
            if (!net.internal && net.family === "IPv4" && net.mac) {
              interfaces.push({
                name,
                mac: net.mac.toUpperCase().replace(/:/g, "-"),
                internal: net.internal,
                family: net.family,
                address: net.address,
              });
            }
          }
        }
      }
      return interfaces;
    }

    console.warn("No method available to get network interfaces");
    return [];
  } catch (error) {
    console.error("Error getting network interfaces:", error);
    return [];
  }
};

// Alternative method using Electron's built-in APIs
const getNetworkInterfacesElectron = async (): Promise<NetworkInterface[]> => {
  try {
    // Check if we're in Electron environment with IPC
    if (typeof window !== "undefined" && window.api) {
      // Use Electron IPC to execute system commands
      const result = await window.api.getMacAddresses();
      return result.map((mac: string, index: number) => ({
        name: `Interface${index}`,
        mac: mac.toUpperCase().replace(/:/g, "-"),
        internal: false,
        family: "IPv4",
        address: "",
      }));
    }

    // Fallback: try direct command execution (if available)
    if (typeof require !== "undefined") {
      const { exec } = require("child_process");
      const util = require("util");
      const os = require("os");
      const execPromise = util.promisify(exec);

      let command: string;
      const platform = os.platform();

      if (platform === "win32") {
        command = "getmac /fo csv /nh";
      } else if (platform === "darwin") {
        command = "ifconfig | grep ether | awk '{print $2}'";
      } else {
        command = "cat /sys/class/net/*/address";
      }

      const { stdout } = await execPromise(command);
      const interfaces: NetworkInterface[] = [];

      if (platform === "win32") {
        // Parse Windows getmac output
        const lines = stdout.trim().split("\n");
        lines.forEach((line, index) => {
          const parts = line.replace(/"/g, "").split(",");
          // Include ALL MAC addresses, even from disconnected interfaces
          if (parts.length >= 2 && parts[1] !== "N/A") {
            interfaces.push({
              name: `Interface${index}`,
              mac: parts[1].toUpperCase().replace(/:/g, "-"),
              internal: false,
              family: "IPv4",
              address: parts[1].includes("MEDIA DISCONNECTED")
                ? "Disconnected"
                : "",
            });
          }
        });
      } else {
        // Parse Unix-like output
        const macs = stdout
          .trim()
          .split("\n")
          .filter((mac) => mac && mac !== "00:00:00:00:00:00");
        macs.forEach((mac, index) => {
          interfaces.push({
            name: `Interface${index}`,
            mac: mac.toUpperCase().replace(/:/g, "-"),
            internal: false,
            family: "IPv4",
            address: "",
          });
        });
      }

      return interfaces;
    }

    console.warn("No method available to get MAC addresses via command");
    return [];
  } catch (error) {
    console.error("Error getting network interfaces via command:", error);
    return [];
  }
};

// Main validation function (matches Java logic)
export const validateMachine = async (): Promise<boolean> => {
  try {
    console.log("🔍 Validating machine MAC address...");

    // Check if we're running in Electron environment
    const isElectron = typeof window !== "undefined" && window.api;
    console.log("🔧 Running in Electron:", isElectron);

    // Try both methods to get network interfaces
    let interfaces = await getNetworkInterfaces();

    if (interfaces.length === 0) {
      console.log("Fallback: Using command-line method...");
      interfaces = await getNetworkInterfacesElectron();
    }

    // If still no interfaces and we're in development mode, use fallback
    if (interfaces.length === 0) {
      if (DEVELOPMENT_MODE && !isElectron) {
        console.log(
          "🛠️ Development mode: Using fallback MAC addresses (browser environment)",
        );
        // Simulate network interfaces with development MAC addresses
        interfaces = DEVELOPMENT_FALLBACK_MACS.map((mac, index) => ({
          name: `DevInterface${index}`,
          mac: mac,
          internal: false,
          family: "IPv4",
          address: "*************",
        }));
      } else {
        console.error("❌ Could not retrieve network interfaces");
        return false;
      }
    }

    console.log("📡 Found network interfaces:");
    interfaces.forEach((iface) => {
      console.log(`   ${iface.name}: ${iface.mac}`);
    });

    // Check if any MAC address matches allowed list
    for (const iface of interfaces) {
      const macAddress = iface.mac;
      console.log(`🔍 Checking MAC: ${macAddress}`);

      if (ALLOWED_MAC_ADDRESSES.includes(macAddress)) {
        console.log(
          `✅ Machine validated! MAC address ${macAddress} is authorized.`,
        );
        return true;
      }
    }

    console.log(
      "❌ Machine validation failed. No authorized MAC address found.",
    );
    console.log("🔒 Allowed MAC addresses:");
    ALLOWED_MAC_ADDRESSES.forEach((mac) => {
      console.log(`   ${mac}`);
    });

    return false;
  } catch (error) {
    console.error("❌ Error during machine validation:", error);
    return false;
  }
};

// Get current machine's MAC addresses (for debugging)
export const getCurrentMacAddresses = async (): Promise<string[]> => {
  try {
    let interfaces = await getNetworkInterfaces();

    if (interfaces.length === 0) {
      interfaces = await getNetworkInterfacesElectron();
    }

    // Fallback for development mode in browser
    if (interfaces.length === 0 && DEVELOPMENT_MODE) {
      const isElectron = typeof window !== "undefined" && window.api;
      if (!isElectron) {
        console.log("🛠️ Development mode: Using fallback MAC addresses");
        return DEVELOPMENT_FALLBACK_MACS;
      }
    }

    return interfaces.map((iface) => iface.mac);
  } catch (error) {
    console.error("Error getting current MAC addresses:", error);
    return DEVELOPMENT_MODE ? DEVELOPMENT_FALLBACK_MACS : [];
  }
};

// Add a new MAC address to the allowed list (for development)
export const addAllowedMacAddress = (macAddress: string): void => {
  const formattedMac = macAddress.toUpperCase().replace(/:/g, "-");
  if (!ALLOWED_MAC_ADDRESSES.includes(formattedMac)) {
    ALLOWED_MAC_ADDRESSES.push(formattedMac);
    console.log(`✅ Added MAC address to allowed list: ${formattedMac}`);
  } else {
    console.log(`ℹ️ MAC address already in allowed list: ${formattedMac}`);
  }
};

// Get the list of allowed MAC addresses
export const getAllowedMacAddresses = (): string[] => {
  return [...ALLOWED_MAC_ADDRESSES];
};

// Check if a specific MAC address is allowed
export const isMacAddressAllowed = (macAddress: string): boolean => {
  const formattedMac = macAddress.toUpperCase().replace(/:/g, "-");
  return ALLOWED_MAC_ADDRESSES.includes(formattedMac);
};

// Machine validation with detailed info
export const validateMachineDetailed = async (): Promise<{
  isValid: boolean;
  currentMacs: string[];
  allowedMacs: string[];
  matchedMac?: string;
  error?: string;
  isElectron?: boolean;
  isDevelopment?: boolean;
}> => {
  try {
    const currentMacs = await getCurrentMacAddresses();
    const allowedMacs = getAllowedMacAddresses();
    const isElectron = typeof window !== "undefined" && window.api;

    for (const mac of currentMacs) {
      if (allowedMacs.includes(mac)) {
        return {
          isValid: true,
          currentMacs,
          allowedMacs,
          matchedMac: mac,
          isElectron,
          isDevelopment: DEVELOPMENT_MODE,
        };
      }
    }

    return {
      isValid: false,
      currentMacs,
      allowedMacs,
      isElectron,
      isDevelopment: DEVELOPMENT_MODE,
    };
  } catch (error) {
    return {
      isValid: false,
      currentMacs: DEVELOPMENT_MODE ? DEVELOPMENT_FALLBACK_MACS : [],
      allowedMacs: getAllowedMacAddresses(),
      error: error instanceof Error ? error.message : "Unknown error",
      isElectron: typeof window !== "undefined" && !!window.api,
      isDevelopment: DEVELOPMENT_MODE,
    };
  }
};

// Initialize machine validation on app start
export const initializeMachineValidation = async (): Promise<boolean> => {
  console.log("🚀 Initializing machine validation...");

  const isValid = await validateMachine();

  if (!isValid) {
    console.log("🚫 Machine validation failed. App access denied.");

    // Show current MAC addresses for debugging
    const currentMacs = await getCurrentMacAddresses();
    console.log("📋 Current machine MAC addresses:");
    currentMacs.forEach((mac) => {
      console.log(`   ${mac}`);
    });

    console.log(
      "💡 To authorize this machine, add one of the above MAC addresses to the ALLOWED_MAC_ADDRESSES array in machineValidator.ts",
    );
  }

  return isValid;
};

// Toggle development mode
export const setDevelopmentMode = (enabled: boolean): void => {
  // Note: This would require making DEVELOPMENT_MODE mutable
  console.log(`🛠️ Development mode ${enabled ? "enabled" : "disabled"}`);
};

// Check if in development mode
export const isDevelopmentMode = (): boolean => {
  return DEVELOPMENT_MODE;
};

// Export the allowed MAC addresses for easy modification
export { ALLOWED_MAC_ADDRESSES };
