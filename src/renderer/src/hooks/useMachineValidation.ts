import { useState, useEffect } from 'react';
import { validateMachine, initializeMachineValidation } from '../lib/machineValidator';

interface MachineValidationState {
  isValidating: boolean;
  isValid: boolean | null;
  error: string | null;
  hasChecked: boolean;
}

export const useMachineValidation = (autoValidate: boolean = true) => {
  const [state, setState] = useState<MachineValidationState>({
    isValidating: false,
    isValid: null,
    error: null,
    hasChecked: false,
  });

  const validate = async () => {
    setState(prev => ({ ...prev, isValidating: true, error: null }));
    
    try {
      const isValid = await validateMachine();
      setState({
        isValidating: false,
        isValid,
        error: null,
        hasChecked: true,
      });
      return isValid;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
      setState({
        isValidating: false,
        isValid: false,
        error: errorMessage,
        hasChecked: true,
      });
      return false;
    }
  };

  const initialize = async () => {
    setState(prev => ({ ...prev, isValidating: true, error: null }));
    
    try {
      const isValid = await initializeMachineValidation();
      setState({
        isValidating: false,
        isValid,
        error: null,
        hasChecked: true,
      });
      return isValid;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
      setState({
        isValidating: false,
        isValid: false,
        error: errorMessage,
        hasChecked: true,
      });
      return false;
    }
  };

  useEffect(() => {
    if (autoValidate) {
      initialize();
    }
  }, [autoValidate]);

  return {
    ...state,
    validate,
    initialize,
    retry: validate,
  };
};
