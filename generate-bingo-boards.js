#!/usr/bin/env node

/**
 * Standalone Bingo Board Generator
 * Generates bingo boards and saves them to a JSON file
 * Can be run independently with: node generate-bingo-boards.js
 */

const fs = require("fs");
const path = require("path");

// Seeded random number generator for deterministic board generation
class SeededRandom {
  constructor(seed) {
    this.seed = seed;
  }

  next() {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  nextInt(min, max) {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }
}

// Constants for board generation
const CONFIG = {
  SEED: 75,
  NUM_BOARDS: 15000,
  BINGO_RANGES: {
    B: { min: 1, max: 15 }, // B column: 1-15
    I: { min: 16, max: 30 }, // I column: 16-30
    N: { min: 31, max: 45 }, // N column: 31-45
    G: { min: 46, max: 60 }, // G column: 46-60
    O: { min: 61, max: 75 }, // O column: 61-75
  },
};

/**
 * Generate unique numbers for a specific column
 */
function generateColumnNumbers(random, min, max, count) {
  const numbers = [];
  while (numbers.length < count) {
    const number = random.nextInt(min, max);
    if (!numbers.includes(number)) {
      numbers.push(number);
    }
  }
  return numbers;
}

/**
 * Generate a single bingo board
 * Returns a flat array of 25 numbers (5 columns x 5 rows)
 */
function generateSingleBoard(random) {
  const board = [];
  const columnNumbers = [];

  // Generate unique numbers for each column (B-I-N-G-O)
  const ranges = Object.values(CONFIG.BINGO_RANGES);

  for (let col = 0; col < 5; col++) {
    const { min, max } = ranges[col];
    // N column only needs 4 numbers (center is FREE)
    const numNeeded = col === 2 ? 4 : 5;
    const colNums = generateColumnNumbers(random, min, max, numNeeded);
    columnNumbers.push(colNums);
  }

  // Build the flat array (25 numbers)
  // Order: column by column (B-I-N-G-O), each column top to bottom
  for (let col = 0; col < 5; col++) {
    for (let row = 0; row < 5; row++) {
      if (row === 2 && col === 2) {
        // Center space is FREE (represented as 0)
        board.push(0);
      } else {
        // Use pre-generated numbers for this column
        // Adjust index for N column due to FREE space
        const colIndex = col === 2 && row > 2 ? row - 1 : row;
        const number = columnNumbers[col][colIndex];
        board.push(number);
      }
    }
  }

  return board;
}

/**
 * Generate all bingo boards
 */
function generateAllBoards() {
  console.log(
    `🎯 Generating ${CONFIG.NUM_BOARDS} bingo boards with seed ${CONFIG.SEED}...`,
  );

  const random = new SeededRandom(CONFIG.SEED);
  const boards = [];

  for (let i = 0; i < CONFIG.NUM_BOARDS; i++) {
    const board = generateSingleBoard(random);
    boards.push(board);

    // Progress indicator
    if ((i + 1) % 500 === 0) {
      console.log(`   Generated ${i + 1}/${CONFIG.NUM_BOARDS} boards...`);
    }
  }

  console.log(`✅ Generated ${boards.length} bingo boards successfully.`);
  return boards;
}

/**
 * Validate a single board (flat array format)
 */
function validateBoard(board, index) {
  const errors = [];

  // Check board structure - should be flat array of 25 numbers
  if (!Array.isArray(board) || board.length !== 25) {
    errors.push(
      `Board ${index}: Invalid board structure - expected array of 25 numbers, got ${board.length}`,
    );
    return errors;
  }

  // Check each position in the flat array (column-major format)
  for (let pos = 0; pos < 25; pos++) {
    const col = Math.floor(pos / 5);
    const row = pos % 5;
    const number = board[pos];

    // Center should be 0 (FREE) - position 12 (col 2, row 2)
    if (col === 2 && row === 2) {
      if (number !== 0) {
        errors.push(
          `Board ${index}: Center space should be 0 (FREE), got ${number}`,
        );
      }
      continue;
    }

    // Check number ranges for each column
    const ranges = Object.values(CONFIG.BINGO_RANGES);
    const { min, max } = ranges[col];

    if (number < min || number > max) {
      errors.push(
        `Board ${index}: Number ${number} in column ${col} is outside range ${min}-${max}`,
      );
    }
  }

  // Check for duplicates in each column (column-major format)
  for (let col = 0; col < 5; col++) {
    const columnNumbers = [];
    for (let row = 0; row < 5; row++) {
      const pos = col * 5 + row;
      if (col === 2 && row === 2) continue; // Skip FREE space (center)
      columnNumbers.push(board[pos]);
    }

    const uniqueNumbers = [...new Set(columnNumbers)];
    if (uniqueNumbers.length !== columnNumbers.length) {
      errors.push(`Board ${index}: Duplicate numbers in column ${col}`);
    }
  }

  return errors;
}

/**
 * Validate all boards
 */
function validateAllBoards(boards) {
  console.log("🔍 Validating boards...");

  let totalErrors = 0;
  const sampleSize = Math.min(100, boards.length);

  for (let i = 0; i < sampleSize; i++) {
    const errors = validateBoard(boards[i], i);
    if (errors.length > 0) {
      console.error(`❌ Board ${i} validation errors:`, errors);
      totalErrors += errors.length;
    }
  }

  if (totalErrors === 0) {
    console.log(`✅ Validation passed for ${sampleSize} sample boards.`);
  } else {
    console.error(
      `❌ Found ${totalErrors} validation errors in sample boards.`,
    );
  }

  return totalErrors === 0;
}

/**
 * Save boards to JSON file
 */
function saveBoardsToJSON(boards) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const filename = `bingo-boards-${CONFIG.SEED}-${timestamp}.json`;

  const data = {
    metadata: {
      generated: new Date().toISOString(),
      seed: CONFIG.SEED,
      totalBoards: boards.length,
      version: "1.0.0",
      description: "Bingo boards generated for Ambesa Bingo game",
      ranges: CONFIG.BINGO_RANGES,
    },
    bingoArrays: boards,
  };

  try {
    fs.writeFileSync(filename, JSON.stringify(data, null, 2));
    console.log(`💾 Boards saved to: ${filename}`);

    const stats = fs.statSync(filename);
    console.log(`📊 File size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);

    return filename;
  } catch (error) {
    console.error("❌ Error saving file:", error);
    throw error;
  }
}

/**
 * Display sample board (flat array format)
 */
function displaySampleBoard(boards) {
  if (boards.length === 0) return;

  console.log("\n📋 Sample Board (Index 0):");
  console.log("   B    I    N    G    O");
  console.log("  ---- ---- ---- ---- ----");

  const board = boards[0];
  for (let row = 0; row < 5; row++) {
    const rowNumbers = [];
    for (let col = 0; col < 5; col++) {
      const pos = col * 5 + row; // Column-major format
      const num = board[pos];
      rowNumbers.push(num === 0 ? "FREE" : num.toString().padStart(4, " "));
    }
    const rowStr = rowNumbers.join(" ");
    console.log(`  ${rowStr}`);
  }

  console.log("\n📋 Flat Array Format:");
  console.log(`[${board.join(", ")}]`);
  console.log("");
}

/**
 * Main execution function
 */
function main() {
  console.log("🎯 Ambesa Bingo Board Generator");
  console.log("================================");
  console.log(`Seed: ${CONFIG.SEED}`);
  console.log(`Boards to generate: ${CONFIG.NUM_BOARDS}`);
  console.log(`Ranges: B(1-15), I(16-30), N(31-45), G(46-60), O(61-75)`);
  console.log("");

  try {
    // Generate boards
    const boards = generateAllBoards();

    // Validate boards
    const isValid = validateAllBoards(boards);
    if (!isValid) {
      console.error("❌ Board validation failed. Aborting.");
      process.exit(1);
    }

    // Display sample
    displaySampleBoard(boards);

    // Save to file
    const filename = saveBoardsToJSON(boards);

    console.log("\n🎉 Generation complete!");
    console.log(`📁 File: ${filename}`);
    console.log(`🎲 Seed: ${CONFIG.SEED}`);
    console.log(`📊 Total boards: ${CONFIG.NUM_BOARDS}`);
    console.log("");
    console.log("💡 Usage:");
    console.log("   - Load the JSON file in your application");
    console.log("   - Access boards with: data.bingoArrays[index]");
    console.log("   - Each board is a flat array of 25 numbers");
    console.log(
      "   - Board indices range from 0 to " + (CONFIG.NUM_BOARDS - 1),
    );
  } catch (error) {
    console.error("❌ Generation failed:", error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

// Export for use as module
module.exports = {
  generateAllBoards,
  validateAllBoards,
  saveBoardsToJSON,
  CONFIG,
};
