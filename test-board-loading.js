// Simple test script to verify board loading from arrays.json
// This can be run in the browser console to test the functionality

console.log('Testing board loading from arrays.json...');

// Import the cartelaGenerator functions
import { getBoardByIndex, getTotalBoards } from './src/renderer/src/lib/cartelaGenerator.ts';

try {
  // Test getting the total number of boards
  const totalBoards = getTotalBoards();
  console.log(`Total boards available: ${totalBoards}`);
  
  // Test getting the first board
  const firstBoard = getBoardByIndex(0);
  console.log('First board:', firstBoard);
  
  // Test getting a few more boards
  for (let i = 0; i < Math.min(5, totalBoards); i++) {
    const board = getBoardByIndex(i);
    console.log(`Board ${i}:`, board);
    
    // Verify board structure
    if (board.length !== 5) {
      console.error(`Board ${i} has incorrect number of columns: ${board.length}`);
    }
    
    for (let col = 0; col < 5; col++) {
      if (board[col].length !== 5) {
        console.error(`Board ${i}, column ${col} has incorrect number of rows: ${board[col].length}`);
      }
    }
    
    // Check if center is FREE (80)
    if (board[2][2] !== 80) {
      console.error(`Board ${i} center is not FREE (80): ${board[2][2]}`);
    }
  }
  
  console.log('Board loading test completed successfully!');
} catch (error) {
  console.error('Board loading test failed:', error);
}
