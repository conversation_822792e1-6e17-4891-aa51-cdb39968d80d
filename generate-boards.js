// Generate and save bingo boards to JSON file
const fs = require("fs");

// Seeded random number generator for deterministic board generation
class SeededRandom {
  constructor(seed) {
    this.seed = seed;
  }

  next() {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  nextInt(min, max) {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }
}

// Constants for board generation (matching Java implementation)
const SEED = 75;
const NUM_CARTELAS = 15000;

// Initialize the cartela numbers using the same logic as Java
const generateAllBoards = () => {
  console.log(`Generating ${NUM_CARTELAS} bingo boards with seed ${SEED}...`);
  const random = new SeededRandom(SEED);
  const boards = [];

  // Generate 5000 boards
  for (let cartelaIndex = 0; cartelaIndex < NUM_CARTELAS; cartelaIndex++) {
    const board = [];

    // Pre-generate unique numbers for each column
    const columnNumbers = [];

    // Generate unique numbers for each column (B-I-N-G-O)
    for (let col = 0; col < 5; col++) {
      let min, max;

      switch (col) {
        case 0: // B column
          min = 1;
          max = 15;
          break;
        case 1: // I column
          min = 16;
          max = 30;
          break;
        case 2: // N column
          min = 31;
          max = 45;
          break;
        case 3: // G column
          min = 46;
          max = 60;
          break;
        case 4: // O column
          min = 61;
          max = 75;
          break;
        default:
          min = 1;
          max = 75;
      }

      // Generate 5 unique numbers for this column (excluding center for N column)
      const colNums = [];
      const numNeeded = col === 2 ? 4 : 5; // N column only needs 4 numbers (center is FREE)

      while (colNums.length < numNeeded) {
        const number = random.nextInt(min, max);
        if (!colNums.includes(number)) {
          colNums.push(number);
        }
      }

      columnNumbers.push(colNums);
    }

    // Build the 5x5 board using the pre-generated column numbers
    for (let row = 0; row < 5; row++) {
      const boardRow = [];

      for (let col = 0; col < 5; col++) {
        if (row === 2 && col === 2) {
          // Center space is FREE (represented as 0)
          boardRow.push(0);
        } else {
          // Use pre-generated numbers for this column
          const colIndex = col === 2 && row > 2 ? row - 1 : row; // Adjust for FREE space in N column
          const number = columnNumbers[col][colIndex];
          boardRow.push(number);
        }
      }

      board.push(boardRow);
    }

    boards.push(board);

    // Progress indicator
    if ((cartelaIndex + 1) % 1000 === 0) {
      console.log(`Generated ${cartelaIndex + 1}/${NUM_CARTELAS} boards...`);
    }
  }

  console.log(`Generated ${boards.length} bingo boards successfully.`);
  return boards;
};

// Save boards to JSON file
const saveBoardsToJSON = (boards) => {
  try {
    const boardsData = {
      metadata: {
        seed: SEED,
        totalBoards: NUM_CARTELAS,
        generatedAt: new Date().toISOString(),
        version: "1.0.0",
        description:
          "Bingo boards generated with seed 75 matching Java implementation",
        columnRanges: {
          B: "1-15",
          I: "16-30",
          N: "31-45",
          G: "46-60",
          O: "61-75",
        },
        notes: [
          "Each board is a 5x5 grid",
          "Center position [2,2] is always FREE (represented as 0)",
          "No duplicate numbers within each column",
          "Board ID corresponds to array index (0-4999)",
        ],
      },
      boards: boards.map((board, index) => ({
        id: index,
        board: board,
        flatArray: board.flat(),
      })),
    };

    const filename = `bingo-boards-seed-${SEED}.json`;
    const jsonString = JSON.stringify(boardsData, null, 2);

    fs.writeFileSync(filename, jsonString);
    console.log(`✅ Boards saved to ${filename}`);
    console.log(
      `📊 File size: ${(fs.statSync(filename).size / 1024 / 1024).toFixed(2)} MB`,
    );

    return filename;
  } catch (error) {
    console.error("❌ Error saving boards to JSON:", error);
    throw error;
  }
};

// Validate a few boards
const validateBoards = (boards) => {
  console.log("\n🔍 Validating boards...");

  for (let i = 0; i < Math.min(5, boards.length); i++) {
    const board = boards[i];
    console.log(`\nBoard #${i}:`);

    // Check board structure
    if (board.length !== 5 || board.some((row) => row.length !== 5)) {
      console.error(`❌ Board #${i} has invalid structure`);
      continue;
    }

    // Check FREE space
    if (board[2][2] !== 0) {
      console.error(`❌ Board #${i} missing FREE space at center`);
      continue;
    }

    // Check column ranges and uniqueness
    let isValid = true;
    const columnRanges = [
      { min: 1, max: 15, name: "B" },
      { min: 16, max: 30, name: "I" },
      { min: 31, max: 45, name: "N" },
      { min: 46, max: 60, name: "G" },
      { min: 61, max: 75, name: "O" },
    ];

    for (let col = 0; col < 5; col++) {
      const columnNumbers = board.map((row) => row[col]).filter((n) => n !== 0);
      const { min, max, name } = columnRanges[col];

      // Check range
      const outOfRange = columnNumbers.filter((n) => n < min || n > max);
      if (outOfRange.length > 0) {
        console.error(
          `❌ Board #${i} column ${name}: numbers out of range: ${outOfRange}`,
        );
        isValid = false;
      }

      // Check uniqueness
      const unique = [...new Set(columnNumbers)];
      if (unique.length !== columnNumbers.length) {
        console.error(
          `❌ Board #${i} column ${name}: duplicate numbers: ${columnNumbers}`,
        );
        isValid = false;
      }

      console.log(`   Column ${name}: ${columnNumbers.join(", ")}`);
    }

    if (isValid) {
      console.log(`✅ Board #${i} is valid`);
    }
  }
};

// Main execution
console.log("🎯 Bingo Board Generator");
console.log("========================");

try {
  const boards = generateAllBoards();
  validateBoards(boards);
  const filename = saveBoardsToJSON(boards);

  console.log("\n🎉 Generation complete!");
  console.log(`📁 File: ${filename}`);
  console.log(`🎲 Seed: ${SEED}`);
  console.log(`📊 Total boards: ${NUM_CARTELAS}`);
} catch (error) {
  console.error("❌ Generation failed:", error);
  process.exit(1);
}
