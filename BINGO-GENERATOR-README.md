# Ambesa Bingo Board Generator

A standalone JavaScript tool for generating bingo boards for the Ambesa Bingo game. This tool creates deterministic bingo boards using a seeded random number generator, ensuring consistent board generation across different runs.

## Features

- ✅ **Deterministic Generation**: Uses seed-based random generation for consistent results
- ✅ **Standard Bingo Rules**: Follows proper BINGO column ranges (B:1-15, I:16-30, N:31-45, G:46-60, O:61-75)
- ✅ **FREE Center Space**: Automatically places FREE space in center (N column, middle row)
- ✅ **No Duplicates**: Ensures no duplicate numbers within each column
- ✅ **Validation**: Built-in board validation to ensure correctness
- ✅ **JSON Output**: Saves boards in structured JSON format with metadata
- ✅ **Standalone**: Runs independently without any dependencies

## Quick Start

### Method 1: Direct Execution

```bash
# Run the generator directly
node generate-bingo-boards.js
```

### Method 2: Using NPM Scripts

```bash
# Copy the package.json for this generator
cp bingo-generator-package.json package.json

# Run using npm
npm run generate
```

### Method 3: Make it Executable (Linux/Mac)

```bash
# Make the script executable
chmod +x generate-bingo-boards.js

# Run directly
./generate-bingo-boards.js
```

## Configuration

Edit the `CONFIG` object in `generate-bingo-boards.js` to customize:

```javascript
const CONFIG = {
  SEED: 75, // Random seed for deterministic generation
  NUM_BOARDS: 15000, // Number of boards to generate
  BINGO_RANGES: {
    // Column ranges (standard bingo)
    B: { min: 1, max: 15 },
    I: { min: 16, max: 30 },
    N: { min: 31, max: 45 },
    G: { min: 46, max: 60 },
    O: { min: 61, max: 75 },
  },
};
```

## Output

The generator creates a JSON file with the following structure:

```json
{
  "metadata": {
    "generated": "2025-07-01T17:03:56.442Z",
    "seed": 75,
    "totalBoards": 5000,
    "version": "1.0.0",
    "description": "Bingo boards generated for Ambesa Bingo game",
    "ranges": {
      "B": { "min": 1, "max": 15 },
      "I": { "min": 16, "max": 30 },
      "N": { "min": 31, "max": 45 },
      "G": { "min": 46, "max": 60 },
      "O": { "min": 61, "max": 75 }
    }
  },
  "boards": [
    [
      [4, 23, 41, 58, 68],
      [7, 19, 39, 51, 70],
      [13, 18, 0, 52, 73], // 0 = FREE space
      [12, 30, 32, 46, 69],
      [1, 28, 45, 48, 62]
    ]
    // ... more boards
  ]
}
```

## Usage in Your Application

### Loading the JSON File

```javascript
const fs = require("fs");

// Load the generated boards
const data = JSON.parse(
  fs.readFileSync("bingo-boards-75-[timestamp].json", "utf8"),
);

// Access metadata
console.log(`Total boards: ${data.metadata.totalBoards}`);
console.log(`Generated with seed: ${data.metadata.seed}`);

// Get a specific board
function getBoardByIndex(index) {
  if (index < 0 || index >= data.boards.length) {
    throw new Error(
      `Board index must be between 0 and ${data.boards.length - 1}`,
    );
  }
  return data.boards[index];
}

// Example: Get board #100
const board = getBoardByIndex(100);
console.log(board);
```

### Board Structure

Each board is a 5x5 array where:

- `board[row][col]` gives you the number at that position
- `board[2][2]` is always `0` (representing the FREE space)
- Column 0 = B (1-15), Column 1 = I (16-30), etc.

### Example Functions

```javascript
// Check if a number is on a board
function hasNumber(board, number) {
  return board.flat().includes(number);
}

// Get all numbers on a board (excluding FREE space)
function getBoardNumbers(board) {
  return board.flat().filter((num) => num !== 0);
}

// Check if board has winning pattern (example: full row)
function hasWinningRow(board, drawnNumbers) {
  const drawnSet = new Set(drawnNumbers);

  for (let row = 0; row < 5; row++) {
    let hasFullRow = true;
    for (let col = 0; col < 5; col++) {
      const number = board[row][col];
      if (number !== 0 && !drawnSet.has(number)) {
        hasFullRow = false;
        break;
      }
    }
    if (hasFullRow) return true;
  }
  return false;
}
```

## Example Usage Script

Run the example to see how to use the generated JSON:

```bash
node example-usage.js
```

This will demonstrate:

- Loading the JSON file
- Accessing individual boards
- Searching for boards with specific numbers
- Validating board structure
- Displaying boards in a readable format

## File Structure

```
├── generate-bingo-boards.js      # Main generator script
├── bingo-generator-package.json  # Package.json for npm usage
├── example-usage.js              # Usage examples
├── BINGO-GENERATOR-README.md     # This documentation
└── bingo-boards-75-[timestamp].json  # Generated output
```

## Validation

The generator includes built-in validation that checks:

- ✅ Board structure (5x5 grid)
- ✅ Center space is FREE (0)
- ✅ Numbers are within correct column ranges
- ✅ No duplicate numbers within columns
- ✅ All boards are properly formatted

## Performance

- **Generation Speed**: ~5000 boards in under 10 seconds
- **File Size**: ~1.85 MB for 5000 boards
- **Memory Usage**: Minimal (processes boards one at a time)

## Integration with Ambesa Bingo

This generator is designed to work seamlessly with the Ambesa Bingo application:

1. **Consistent Seed**: Uses seed 75 to match your existing board generation
2. **Compatible Format**: Boards are in the same 5x5 array format
3. **Standard Ranges**: Follows proper BINGO column number ranges
4. **FREE Space**: Center space is represented as 0, matching your current logic

## Troubleshooting

### Common Issues

1. **"Cannot find module"**: Make sure you're running from the correct directory
2. **Permission denied**: On Linux/Mac, run `chmod +x generate-bingo-boards.js`
3. **Out of memory**: Reduce `NUM_BOARDS` in the config for large generations
4. **Invalid JSON**: Check that the generation completed successfully

### Validation Errors

If you see validation errors:

- Check that column ranges are correct
- Ensure the seed produces valid random numbers
- Verify board structure is 5x5

## License

MIT License - Feel free to modify and use in your Ambesa Bingo project.

## Support

For issues or questions about this generator, check:

1. The example-usage.js file for implementation examples
2. The validation output for any structural issues
3. The generated JSON metadata for configuration details
