import { io } from "socket.io-client";

console.log("Testing Bingo server connection...");

const socket = io("http://localhost:5000", {
  transports: ["websocket"],
  reconnection: false,
});

socket.on("connect", () => {
  console.log("✅ Connected to Bingo server!");
  console.log("📡 Requesting Bingo game details...");
  socket.emit("getBingoGameDetails");
});

socket.on("bingoGameStatus", (data) => {
  console.log("🎮 Received game status:", JSON.stringify(data, null, 2));
});

socket.on("bingoNumberDrawn", (data) => {
  console.log(`🎱 Number drawn: ${data.number} (${data.drawnCount}/${data.totalNumbers})`);
});

socket.on("bingoDrawnNumbers", (data) => {
  console.log("🏆 Round completed:", JSON.stringify(data, null, 2));
  socket.disconnect();
  process.exit(0);
});

socket.on("connect_error", (error) => {
  console.error("❌ Connection failed:", error.message);
  process.exit(1);
});

socket.on("disconnect", (reason) => {
  console.log("🔌 Disconnected:", reason);
});

// Timeout after 30 seconds to see some number draws
setTimeout(() => {
  console.log("⏰ Test completed");
  socket.disconnect();
  process.exit(0);
}, 30000);
