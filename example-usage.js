/**
 * Example: How to use the generated bingo boards JSON file
 */

const fs = require('fs');

// Example usage of the generated bingo boards
function exampleUsage() {
  try {
    // Load the generated JSON file (replace with actual filename)
    const filename = 'bingo-boards-75-2025-07-01T17-03-56-440Z.json';
    const data = JSON.parse(fs.readFileSync(filename, 'utf8'));
    
    console.log('📊 Bingo Boards Data Loaded');
    console.log('============================');
    console.log(`Generated: ${data.metadata.generated}`);
    console.log(`Seed: ${data.metadata.seed}`);
    console.log(`Total Boards: ${data.metadata.totalBoards}`);
    console.log(`Version: ${data.metadata.version}`);
    console.log('');

    // Display column ranges
    console.log('🎯 Column Ranges:');
    Object.entries(data.metadata.ranges).forEach(([letter, range]) => {
      console.log(`   ${letter}: ${range.min}-${range.max}`);
    });
    console.log('');

    // Get a specific board by index
    function getBoardByIndex(index) {
      if (index < 0 || index >= data.boards.length) {
        throw new Error(`Board index must be between 0 and ${data.boards.length - 1}`);
      }
      return data.boards[index];
    }

    // Display a board in a nice format
    function displayBoard(board, title = 'Bingo Board') {
      console.log(`📋 ${title}:`);
      console.log('   B    I    N    G    O');
      console.log('  ---- ---- ---- ---- ----');
      
      for (let row = 0; row < 5; row++) {
        const rowStr = board[row]
          .map(num => num === 0 ? 'FREE' : num.toString().padStart(4, ' '))
          .join(' ');
        console.log(`  ${rowStr}`);
      }
      console.log('');
    }

    // Example 1: Get and display first board
    const firstBoard = getBoardByIndex(0);
    displayBoard(firstBoard, 'First Board (Index 0)');

    // Example 2: Get and display a random board
    const randomIndex = Math.floor(Math.random() * data.boards.length);
    const randomBoard = getBoardByIndex(randomIndex);
    displayBoard(randomBoard, `Random Board (Index ${randomIndex})`);

    // Example 3: Search for boards containing specific numbers
    function findBoardsWithNumbers(searchNumbers) {
      const matchingBoards = [];
      
      for (let i = 0; i < data.boards.length; i++) {
        const board = data.boards[i];
        const flatBoard = board.flat().filter(num => num !== 0); // Exclude FREE space
        
        const hasAllNumbers = searchNumbers.every(num => flatBoard.includes(num));
        if (hasAllNumbers) {
          matchingBoards.push(i);
        }
      }
      
      return matchingBoards;
    }

    // Example search
    const searchNumbers = [5, 23, 45];
    const matchingBoards = findBoardsWithNumbers(searchNumbers);
    console.log(`🔍 Search Results for numbers [${searchNumbers.join(', ')}]:`);
    console.log(`   Found ${matchingBoards.length} matching boards`);
    
    if (matchingBoards.length > 0) {
      console.log(`   First 5 matching board indices: ${matchingBoards.slice(0, 5).join(', ')}`);
      
      // Display first matching board
      const firstMatch = getBoardByIndex(matchingBoards[0]);
      displayBoard(firstMatch, `First Matching Board (Index ${matchingBoards[0]})`);
    }

    // Example 4: Validate a board structure
    function validateBoardStructure(board) {
      const errors = [];
      
      // Check basic structure
      if (!Array.isArray(board) || board.length !== 5) {
        errors.push('Invalid board structure');
        return errors;
      }
      
      // Check each row
      for (let row = 0; row < 5; row++) {
        if (!Array.isArray(board[row]) || board[row].length !== 5) {
          errors.push(`Invalid row ${row} structure`);
          continue;
        }
        
        // Check center is FREE
        if (row === 2 && board[row][2] !== 0) {
          errors.push('Center space should be FREE (0)');
        }
        
        // Check column ranges
        for (let col = 0; col < 5; col++) {
          if (row === 2 && col === 2) continue; // Skip FREE space
          
          const number = board[row][col];
          const ranges = Object.values(data.metadata.ranges);
          const { min, max } = ranges[col];
          
          if (number < min || number > max) {
            errors.push(`Number ${number} in column ${col} is outside range ${min}-${max}`);
          }
        }
      }
      
      return errors;
    }

    // Validate a few boards
    console.log('✅ Board Validation:');
    for (let i = 0; i < 3; i++) {
      const board = getBoardByIndex(i);
      const errors = validateBoardStructure(board);
      
      if (errors.length === 0) {
        console.log(`   Board ${i}: Valid ✓`);
      } else {
        console.log(`   Board ${i}: Invalid ✗ - ${errors.join(', ')}`);
      }
    }

    console.log('\n🎉 Example completed successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run the example
if (require.main === module) {
  exampleUsage();
}

module.exports = { exampleUsage };
